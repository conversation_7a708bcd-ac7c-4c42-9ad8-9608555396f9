import { Box, Button } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React from "react";
import { useParams } from "react-router-dom";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import tenantsService from "src/services/tenants.service";
import { z } from "zod";

interface TenantCompensationProps {
  featureKey: string;
  isAddWorkRoleFeatureActive?: boolean;
}

const submitSchema = z.object({
  aggregatorType: z.string({
    message: "required",
  }),
});

const compensationAggregates: React.FC<TenantCompensationProps> = () => {
  const { tenantId = "" } = useParams();
  const { data: compensationAggregation, refetch } = useQuery(
    ["compensation-aggregation"],
    async () => tenantsService.getCompensationAggregation(tenantId),
    {
      enabled: !!tenantId,
      refetchOnWindowFocus: false,
    },
  );

  const updateCompensationAggregation = useMutation({
    mutationKey: ["update-compensation-aggregation"],
    mutationFn: async (aggregatorType?: string | null) =>
      tenantsService.updateCompensationAggregation(aggregatorType as string, tenantId, !!compensationAggregation),
    onSuccess: () => {
      refetch();
    },
  });

  const form = useAppForm({
    defaultValues: {
      aggregatorType: compensationAggregation || "ctc",
    },
    validators: {
      onChange: submitSchema,
    },
    onSubmit: (props) => {
      updateCompensationAggregation.mutate(props.value.aggregatorType);
    },
  });

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      <ContentHeader
        title="Payroll Configuration"
        subtitle="Configure payroll settings for your organisation"
        actions={
          <form.Subscribe selector={(state) => [state.canSubmit, state.errorMap]}>
            {([canSubmit, errorMap]) => {
              return (
                <Button disabled={!canSubmit} variant="contained" onClick={form.handleSubmit}>
                  Save
                </Button>
              );
            }}
          </form.Subscribe>
        }
      />
      <form.AppField name="aggregatorType">
        {(field: any) => (
          <field.EffiRadioGroup
            label="Compensation structure based on:"
            disabled={updateCompensationAggregation.isLoading}
            layout="vertical"
            required
            options={[
              { label: "CTC", value: "ctc" },
              { label: "Gross", value: "gross" },
            ]}
          />
        )}
      </form.AppField>
    </Box>
  );
};

export default compensationAggregates;
