import { Box, Tab, Tabs } from "@mui/material";
import React, { useMemo } from "react";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import CompensationAggregates from "./CompensationAggregates";
import CostCentre from "./CostCentre";
import TenantWorkRoles from "./TenantWorkRoles";

const TenantConfigurations = () => {
  const TENANT_ACL = getACLFromFeaturekey(PATH_CONFIG.TENANTS_EDIT.key);

  const configurationTabs = useMemo(
    () => [
      {
        key: "costCentre",
        label: "Cost Centre",
        component: <CostCentre featureKey={PATH_CONFIG.TENANTS_EDIT.key} />,
        id: 0,
      },
      {
        key: "workRole",
        label: "Work Role",
        component: <TenantWorkRoles featureKey={PATH_CONFIG.TENANTS_EDIT.key} />,
        id: 1,
      },
      {
        key: "compensationAggregates",
        label: "Payroll",
        component: <CompensationAggregates featureKey={PATH_CONFIG.TENANTS_EDIT.key} />,
        id: 1,
      },
    ],
    [TENANT_ACL],
  );
  const [value, setValue] = React.useState(configurationTabs[0].id);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box
      sx={{
        maxHeight: "100%",
        display: "flex",
        gap: 2,
      }}
    >
      <Tabs
        orientation="vertical"
        variant="scrollable"
        value={value}
        onChange={handleChange}
        aria-label="Vertical tabs example"
        sx={{
          borderRight: 1,
          borderColor: "divider",
          width: "10%",
        }}
      >
        {configurationTabs.map((tab) => (
          <Tab
            sx={{
              textTransform: "none",
              padding: "12px 0px",
              alignItems: "flex-start",
              wordBreak: "break-word",
            }}
            key={tab.label}
            label={tab.label}
          />
        ))}
      </Tabs>
      <Box sx={{ width: "100%" }}>{configurationTabs[value].component}</Box>
    </Box>
  );
};

export default TenantConfigurations;
