import { Autocomplete, Box, Chip, InputAdornment, ListItem, Paper } from "@mui/material";
import { UseQueryResult, useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import { SearchIcon } from "src/assets/icons.svg";
import useDebounce from "src/customHooks/useDebounce";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import {
  OmniSearchResponse,
  OmniSearchResult,
  OmniSearchResultKeys,
} from "src/services/api_definitions/search.service";
import SearchServiceAPI from "src/services/search.service";
import { useFieldContext } from "../../effiFormContext";

interface EmployeeSearchPaperProps {
  children?: React.ReactNode;
}

const EmployeeSearchPaper: React.FC<EmployeeSearchPaperProps> = ({ children }) => {
  return (
    <Paper
      sx={{
        maxHeight: 200,
        overflow: "auto",
        "& .MuiAutocomplete-listbox": {
          padding: 0,
        },
      }}
    >
      {children}
    </Paper>
  );
};

interface EffiEmployeeMultiSelectProps {
  getData?: OmniSearchResultKeys;
  label: string;
  required?: boolean;
  placeholder?: string;
  searchField?: OmniSearchResultKeys;
}

const EffiEmployeeMultiSelect: React.FC<EffiEmployeeMultiSelectProps> = ({
  getData = "email",
  label,
  required = false,
  placeholder = "Search employees",
  searchField = "value",
}) => {
  const field = useFieldContext();
  const [inputValue, setInputValue] = useState("");
  const debouncedValue = useDebounce(inputValue, 400);

  const { data, isLoading }: UseQueryResult<OmniSearchResponse> = useQuery({
    queryKey: ["employee-search", debouncedValue],
    queryFn: async () => {
      if (debouncedValue) {
        const result = await SearchServiceAPI.searchValue(debouncedValue);
        // Temporary debugging
        if (debouncedValue.length > 2) {
          console.log("Search term:", debouncedValue, "Result:", result);
        }
        return result || []; // Handle null case
      }
      return [];
    },
    enabled: !!debouncedValue,
    refetchOnWindowFocus: false,
  });

  const selectedEmails = field.state.value || [];
  const searchOptions = data || [];

  // Find selected options from current search results or create placeholder options
  const selectedOptions = selectedEmails.map((email: string) => {
    const foundOption = searchOptions.find((option) => option[getData] === email);
    return foundOption || { [getData]: email, value: email, email, href: "", code: "" };
  });

  const handleChange = (newValue: OmniSearchResult[]) => {
    const emails = newValue.map((option) => option[getData]);
    field.handleChange(emails);
  };

  const handleSearchInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newInputValue = event.target.value;
    console.log("Direct input change:", newInputValue);
    setInputValue(newInputValue);
  };

  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} data-testId={field.name} />
      <Autocomplete
        multiple
        options={searchOptions}
        value={selectedOptions}
        onChange={(_, newValue) => handleChange(newValue)}
        inputValue={inputValue}
        getOptionLabel={(option) => option.value || ""}
        isOptionEqualToValue={(option, value) => option[getData] === value[getData]}
        filterOptions={(options) => options} // Don't filter, let the API handle it
        PaperComponent={EmployeeSearchPaper}
        noOptionsText={debouncedValue ? "No employees found" : "Type to search employees"}
        loading={isLoading}
        renderTags={(tagValue, getTagProps) =>
          tagValue.map((option, index) => (
            <Chip
              label={option.value}
              {...getTagProps({ index })}
              key={option[getData]}
              size="small"
              sx={{ margin: 0.5 }}
            />
          ))
        }
        renderInput={(params) => (
          <CustomTextField
            {...params}
            placeholder={placeholder}
            size="small"
            onChange={handleSearchInputChange}
            error={field.state?.meta?.errors?.length > 0}
            helperText={field.state?.meta?.errors?.map((err) => err.message).join(", ")}
            InputProps={{
              ...params.InputProps,
              startAdornment: (
                <>
                  <InputAdornment position="start" sx={{ marginLeft: "6px", marginRight: "0.2rem" }}>
                    <SearchIcon />
                  </InputAdornment>
                  {params.InputProps.startAdornment}
                </>
              ),
            }}
          />
        )}
        renderOption={(props, option) => (
          <ListItem
            {...props}
            style={{
              padding: "8px 16px",
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <Box display="flex" flexDirection="column">
              <Box fontWeight={500}>{option.value}</Box>
              {option.email && (
                <Box fontSize="0.875rem" color="text.secondary">
                  {option.email}
                </Box>
              )}
            </Box>
          </ListItem>
        )}
      />
    </Box>
  );
};

export default EffiEmployeeMultiSelect;
