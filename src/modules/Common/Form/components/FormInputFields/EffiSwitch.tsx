import { Box, FormControlLabel, Switch } from "@mui/material";
import React from "react";
import { useFieldContext } from "../../effiFormContext";

type EffiSwitchProps = {
  label: string;
  required?: boolean;
};

const EffiSwitch: React.FC<EffiSwitchProps> = ({ label, required }) => {
  const field = useFieldContext();
  return (
    <Box display="flex" flexDirection="column">
      <FormControlLabel
        label={label}
        required={required}
        control={
          <Switch
            id={field.name}
            name={field.name}
            data-testId={field.name}
            checked={!!field.state.value}
            onChange={(_ev, checked) => field.handleChange(checked)}
          />
        }
      />
    </Box>
  );
};

export default EffiSwitch;
