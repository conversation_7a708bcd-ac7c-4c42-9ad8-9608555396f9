import { CheckCircleOutline, Error, UploadFileRounded, Warning } from "@mui/icons-material";
import { Box, styled, Typography } from "@mui/material";
import React from "react";

export const CustomDropzoneContainer = styled(Box)(() => ({
  background: "#E6F2F1",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const getDefaultSVGIcon = (files: File[]) => {
  if (files?.length > 0 && files?.every((file) => file?.type === "image/jpeg" || file?.type === "image/png")) {
    return files?.map((file) => (
      <img width={54} height={54} key={file.name} src={URL.createObjectURL(file)} alt={file.name} />
    ));
  }
  return <UploadFileRounded sx={{ width: 54, height: 54, cursor: "pointer" }} />;
};

export const styleConfig = (
  variant: "default" | "success" | "error" | "warning" | "disabled",
  text?: string,
  files?: File[],
) => {
  switch (variant) {
    case "disabled":
      return {
        borderColor: "rgba(0, 0, 0, 0.4)",
        backgroundActiveColor: "#E6F2F1",
        backgroundColor: "rgba(0, 0, 0, 0.05)",
        svgIcon: () => <UploadFileRounded sx={{ width: 54, height: 54 }} />,
        displayText: () => (
          <Typography fontSize={12}>{text ? text : "Drag and drop file here or Click to upload."}</Typography>
        ),
      };
    case "default":
      return {
        borderColor: "#007F6F",
        backgroundActiveColor: "#007F6F",
        backgroundColor: "#E6F2F1",
        svgIcon: () => getDefaultSVGIcon(files as File[]),
        displayText: () => (
          <Typography fontSize={12}>{text ? text : "Drag and drop file here or Click to upload."}</Typography>
        ),
      };
    case "success":
      return {
        borderColor: "#007F6F",
        backgroundActiveColor: "transparent",
        backgroundColor: "transparent",
        svgIcon: () => <CheckCircleOutline sx={{ width: 54, height: 54 }} style={{ color: "#007F6F" }} />,
        displayText: () => (
          <Box>
            <Typography fontSize={12} variant="h6" sx={{ color: "#007F6F" }} textAlign={"center"}>
              Success
            </Typography>
            <Typography fontSize={12}>{text ? text : "File has been uploaded successfully."}</Typography>
            {files &&
              files?.length > 0 &&
              files?.every((file) => file?.type === "image/jpeg" || file?.type === "image/png") &&
              files?.map((file) => (
                <img width={54} height={54} key={file.name} src={URL.createObjectURL(file)} alt={file.name} />
              ))}
          </Box>
        ),
      };
    case "error":
      return {
        borderColor: "#FF4D4D",
        backgroundActiveColor: "transparent",
        backgroundColor: "transparent",
        svgIcon: () => <Error sx={{ width: 64, height: 64 }} style={{ color: "#FF4D4D" }} />,
        displayText: () => (
          <Box>
            <Typography fontSize={12} variant="h6" sx={{ color: "#FF4D4D" }} textAlign={"center"}>
              Failed, click to upload again.
            </Typography>
            <Typography
              height={"150px"}
              overflow={"scroll"}
              fontSize={12}
              dangerouslySetInnerHTML={{ __html: text || "Your CSV file import has failed." }}
            />
          </Box>
        ),
      };
    case "warning":
      return {
        borderColor: "#FFBF00",
        backgroundActiveColor: "#FBFAF7",
        backgroundColor: "#FBFAF7",
        svgIcon: () => <Warning sx={{ width: 54, height: 54 }} style={{ color: "#FFBF00" }} />,
        displayText: () => (
          <Box>
            <Typography variant="h6" sx={{ color: "#FFBF00" }} textAlign={"center"}>
              Warning
            </Typography>
            <Typography>{text ? text : "The import of your CSV file has either stopped or failed midway."}</Typography>
          </Box>
        ),
      };
  }
};
