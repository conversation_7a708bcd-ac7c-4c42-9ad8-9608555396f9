import { PersonRemove, Refresh } from "@mui/icons-material";
import { Box, Chip, Typography } from "@mui/material";
import { MRT_ColumnDef } from "material-react-table";
import React from "react";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { PayrunEmployeeDetail } from "src/services/api_definitions/payroll.service";
import ReadOnlyEmployeePayrunView from "../Common/ReadOnlyEmployeePayrunView";
import { EditPayrunDetailModes } from "./EditPayrunDetail";

interface IncludedPayrunEmployeesProps {
  payRunDetails: PayrunEmployeeDetail[] | null;
  isLoading: boolean;
  isFetched: boolean;
  setEmployeeToEdit: (employee: any) => void;
  setEditPayrunDetailMode: (mode: any) => void;
  onExcludeEmployee: (ids: string[]) => void;
  onRefreshEmployee: (ids: string[]) => void;
  selectedRows: Record<number, boolean>;
  setSelectedRows: React.Dispatch<React.SetStateAction<Record<number, boolean>>>;
  hideActions?: boolean;
}

const IncludedPayrunEmployees: React.FC<IncludedPayrunEmployeesProps> = ({
  payRunDetails,
  isLoading,
  isFetched,
  setEmployeeToEdit,
  setEditPayrunDetailMode,
  onExcludeEmployee,
  onRefreshEmployee,
  selectedRows,
  setSelectedRows,
  hideActions,
}) => {
  const [employeeDetails, setEmployeeDetails] = React.useState<PayrunEmployeeDetail | null>(null);

  const columns: MRT_ColumnDef<PayrunEmployeeDetail>[] = [
    {
      accessorKey: "employee.display_name",
      header: "Employee",
      Cell: ({ row }) => (
        <Box display="flex" gap={1}>
          <EmployeeCellInfo
            name={row.original.employee.display_name}
            jobTitle={row.original.employee.job_title}
            displayPic={row.original.employee.display_pic}
            onLinkClick={() => setEmployeeDetails(row.original)}
          />
          {row.original?.modified && (
            <Chip color="primary" label={<Typography fontSize={12}>Modified</Typography>} size="small" />
          )}
        </Box>
      ),
    },
    {
      accessorKey: "paid_days",
      header: "Paid Days",
    },
    {
      accessorKey: "gross_pay",
      header: "Gross Pay",
      Cell: ({ row }) => formatCurrency(row?.original?.gross_pay),
    },
    {
      accessorKey: "earnings",
      header: "Earnings",
      Cell: ({ row }) => formatCurrency(row?.original?.earnings),
    },
    {
      accessorKey: "deductions",
      header: "Deductions",
      Cell: ({ row }) => formatCurrency(row?.original?.deductions),
    },
    {
      accessorKey: "tax_amount",
      header: "TDS",
      Cell: ({ row }) => formatCurrency(row?.original?.tax_amount),
    },
    {
      accessorKey: "net_pay",
      header: "Net Amount",
      Cell: ({ row }) => formatCurrency(row?.original?.net_pay),
    },
  ];
  return (
    <>
      {employeeDetails && (
        <ReadOnlyEmployeePayrunView
          employeeToEdit={employeeDetails}
          onClose={() => setEmployeeDetails(null)}
          isOpen={!!employeeDetails}
        />
      )}
      <DataTable
        enableRowSelection
        enableBatchRowSelection
        enableRowActions={!hideActions}
        positionActionsColumn="last"
        onRowSelectionChange={setSelectedRows}
        data={payRunDetails || []}
        state={{
          showSkeletons: isLoading || !isFetched,
          columnPinning: {
            right: ["mrt-row-actions"],
          },
          rowSelection: selectedRows,
        }}
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 200,
            maxSize: 200,
            header: "",
            muiTableBodyCellProps: {
              align: "right",
            },
            enablePinning: true,
          },
        }}
        renderRowActions={({ row }) => (
          <TableActions
            key={row.id}
            edit={{
              onClick: () => {
                setEmployeeToEdit(row.original);
                setEditPayrunDetailMode(EditPayrunDetailModes.EDIT);
              },
            }}
            remove={{
              onClick: () => {},
              hide: true,
            }}
            view={{
              onClick: () => {},
              hide: true,
            }}
            renderCustomActions={() => [
              {
                title: "Exclude",
                Icon: PersonRemove,
                tooltip: "Exclude",
                onClick: () => onExcludeEmployee([row.original.id]),
              },
              {
                title: "Refresh",
                Icon: Refresh,
                onClick: () => onRefreshEmployee([row.original.id]),
                tooltip: "Refresh",
              },
            ]}
          />
        )}
        columns={columns}
      />
    </>
  );
};

export default IncludedPayrunEmployees;
