import { Close } from "@mui/icons-material";
import { Box, Button, Chip, Paper } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import {
  CompensationComponent,
  PayrunEmployeeDetail,
  UpdateEmployeeDetails,
} from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { z } from "zod";
import { PayrunDetailsContext } from "./PayrunDetails";

interface AddBulkPayrunProps {
  isModalOpen: boolean;
  onClose: () => void;
  selectedRows: PayrunEmployeeDetail[];
  allCompensations?: CompensationComponent[];
}

const bulkSupplementaryPaySchema = z.object({
  component_type: z.enum(["Earning", "Deduction"]),
  pay_type: z.string().optional().nullable(),
  formula: z.object({
    value: z.number(),
    display_name: z.string().optional().nullable(),
  }),
  adjustment_note: z.string().optional().nullable(),
});

const AdHocPayModal: React.FC<AddBulkPayrunProps> = ({ isModalOpen, onClose, selectedRows }) => {
  if (!isModalOpen) return null;
  const [selectedChips, setSelectedChips] = React.useState<any[]>([...selectedRows]);
  const { allCompensations, isLoading } = React.useContext(PayrunDetailsContext);

  const isAdHocComponent = (component: CompensationComponent) => {
    return !!(component?.attributes?.find((eachAttribute) => Reflect.has(eachAttribute, "ad_hoc")) as any)?.["ad_hoc"];
  };

  const adHocEarnings = allCompensations
    ?.filter(
      (eachCompensation) =>
        eachCompensation?.component_type === "Earning" &&
        eachCompensation?.active &&
        isAdHocComponent(eachCompensation),
    )
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));

  const adHocDeductions = allCompensations
    ?.filter(
      (eachCompensation) =>
        eachCompensation?.component_type === "Deduction" &&
        eachCompensation?.active &&
        isAdHocComponent(eachCompensation),
    )
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));

  const updateEmployeePayrunMutation = useMutation({
    mutationKey: ["update-employee-payrun-detail"],
    mutationFn: async (payload: UpdateEmployeeDetails) => payrollService.updateEmployeeDetailsInBulk(payload),
    onSuccess: () => {
      onClose();
    },
  });

  const form = useAppForm({
    defaultValues: {
      component_type: "Earning",
      pay_type: "",
      formula: {
        value: 0,
        display_name: null,
      },
      adjustment_note: "",
    },
    validators: {
      onSubmit: bulkSupplementaryPaySchema as any,
    },
    onSubmit: ({ value }) => {
      console.log({ value });
      updateEmployeePayrunMutation.mutate({
        employee_pay_run_ids: selectedChips.map((eachChip) => eachChip.id),
        components: [
          {
            compensation_component_id: allCompensations.find(
              (eachCompensation) => eachCompensation?.name === value.pay_type,
            )?.id as string,
            formula: {
              ...value.formula,
              value: value.formula.value as unknown as string,
            },
            adjustment_note: value.adjustment_note,
          },
        ],
      });
    },
  });
  const { component_type, formula } = useStore(form.store, (state) => state.values);

  const onDelete = (ev: React.ChangeEvent, index: number) => {
    ev.preventDefault();
    ev.stopPropagation();
    setSelectedChips((prevChips) => {
      const elementToRemove = prevChips[index];
      const newChips = [...prevChips].filter((chip) => chip !== elementToRemove);
      return newChips;
    });
  };

  return (
    <Modal
      isOpen={isModalOpen}
      title="Add Bulk Payrun"
      onClose={onClose}
      showBackButton
      showDivider
      fullwidth
      actions={
        <Box display="flex" p={2} gap={1} justifyContent="flex-end">
          <Button variant="outlined" onClick={() => onClose()}>
            Cancel
          </Button>
          <Button variant="contained" type="submit" onClick={form.handleSubmit}>
            Save
          </Button>
        </Box>
      }
    >
      <Box display="flex" flexDirection="column" gap={1}>
        <Box width="100%" display="flex" gap={2}>
          {selectedChips?.map((eachChip, index) => (
            <Chip
              key={eachChip.id}
              deleteIcon={selectedChips?.length === 1 ? <></> : <Close />}
              onDelete={(ev) => onDelete(ev, index)}
              sx={{ background: "#E6F2F1", borderRadius: 2, minWidth: 120 }}
              label={eachChip.employee.display_name}
            />
          ))}
        </Box>
        <form.AppField name="component_type">
          {(field: any) => (
            <field.EffiRadioGroup
              label="Calculation Type"
              layout="horizontal"
              options={[
                { label: "Earning", value: "Earning" },
                { label: "Deduction", value: "Deduction" },
              ]}
            />
          )}
        </form.AppField>
        <form.AppField
          name="pay_type"
          listeners={{
            onChange: ({ value }: { value: string }) => {
              form.setFieldValue(
                "formula",
                allCompensations.find((eachCompensation) => eachCompensation?.name === value)?.formula,
              );
            },
          }}
        >
          {(field: any) => (
            <field.EffiSelect
              label={component_type === "Earning" ? "Earning Type" : "Deduction Type"}
              disabled={isLoading}
              options={component_type === "Earning" ? adHocEarnings : adHocDeductions}
            />
          )}
        </form.AppField>
        <Box component={Paper} elevation={2} p={2} display="flex" flexDirection="column" gap={2}>
          <form.AppField name="formula.calculation_type">
            {(field: any) => (
              <field.EffiRadioGroup
                label="Calculation Type"
                layout="horizontal"
                disabled
                options={[
                  { label: "Flat Amount", value: "Flat" },
                  { label: "Percentage", value: "Percentage" },
                ]}
              />
            )}
          </form.AppField>
          <Box display="flex" gap={1}>
            <form.AppField name="formula.value">
              {(field: any) => {
                if (formula?.calculation_type === "Percentage") {
                  return (
                    <field.EffiPercentageField
                      label=""
                      required
                      placeholder="Enter percentage"
                      endHelperText={`of ${formula?.display_name}`}
                    />
                  );
                }
                return <field.EffiCurrency label="" required currency="INR" placeholder="Enter amount" />;
              }}
            </form.AppField>
          </Box>
        </Box>
        <form.AppField name="adjustment_note">
          {(field: any) => <field.EffiTextField label="Adjustment Note" />}
        </form.AppField>
      </Box>
    </Modal>
  );
};

export default AdHocPayModal;
