import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useState } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { CompensationComponent, PayRunDetail } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import AllExcludedPayruns from "./AllExcludedPayruns";
import AllPayrunDetails from "./AllPayrunDetails";
import EditPayrunDetail from "./EditPayrunDetail";

interface PayrunDetailsProps {
  payruns?: PayRunDetail[];
  refetch: () => void;
  isExcludedEmployeesView: boolean;
  setIsExcludedEmployeesView: (value: boolean) => void;
  isMasked?: boolean;
  isAdHocPayrunFlow?: boolean;
}

export enum PayrunDetailsViewModes {
  VIEW_ALL_PAYRUN_DETAILS,
  EDIT_PAYRUN_DETAIL,
  VIEW_EMPLOYEE_DETAILS,
  VIEW_ALL_EXCLUDED_EMPLOYEES,
}

export const PayrunDetailsContext = React.createContext({
  allCompensations: [] as CompensationComponent[],
  isLoading: false,
  refetch: () => {},
  isMasked: false,
  isAdHocPayrunFlow: false,
});

const PayrunDetails: React.FC<PayrunDetailsProps> = ({
  payruns,
  refetch,
  isExcludedEmployeesView,
  setIsExcludedEmployeesView,
  isMasked = false,
  isAdHocPayrunFlow = false,
}) => {
  const [payrunDetailViewModes, setPayrunDetailsViewModes] = useState<PayrunDetailsViewModes>(
    PayrunDetailsViewModes.VIEW_ALL_PAYRUN_DETAILS,
  );
  const [selectedPayrun, setSelectedPayrun] = useState<PayRunDetail | null>(null);
  const dispatch = useAppDispatch();

  const { data: allCompensations, isLoading } = useQuery(
    ["get-all-compensations"],
    async () => {
      return payrollService.getAllCompensationComponents("India");
    },
    {
      refetchOnWindowFocus: false,
    },
  );

  useEffect(() => {
    if (payrunDetailViewModes === PayrunDetailsViewModes.VIEW_ALL_PAYRUN_DETAILS) {
      setSelectedPayrun(null);
      dispatch(setFullviewMode(false));
    }

    if (isExcludedEmployeesView) {
      setPayrunDetailsViewModes(PayrunDetailsViewModes.VIEW_ALL_EXCLUDED_EMPLOYEES);
    }
  }, [payrunDetailViewModes, isExcludedEmployeesView]);

  switch (payrunDetailViewModes) {
    case PayrunDetailsViewModes.EDIT_PAYRUN_DETAIL:
      return (
        <PayrunDetailsContext.Provider
          value={{
            allCompensations: allCompensations as CompensationComponent[],
            isLoading,
            refetch,
            isMasked,
            isAdHocPayrunFlow,
          }}
        >
          <EditPayrunDetail payrun={selectedPayrun} setPayrunDetailsViewModes={setPayrunDetailsViewModes} />
        </PayrunDetailsContext.Provider>
      );
    case PayrunDetailsViewModes.VIEW_ALL_EXCLUDED_EMPLOYEES:
      return (
        <PayrunDetailsContext.Provider
          value={{
            allCompensations: allCompensations as CompensationComponent[],
            isLoading,
            refetch,
            isMasked,
            isAdHocPayrunFlow,
          }}
        >
          <AllExcludedPayruns
            onClose={() => {
              setIsExcludedEmployeesView(false);
              dispatch(setFullviewMode(false));
              setPayrunDetailsViewModes(PayrunDetailsViewModes.VIEW_ALL_PAYRUN_DETAILS);
            }}
          />
        </PayrunDetailsContext.Provider>
      );
    default:
      return (
        <PayrunDetailsContext.Provider
          value={{
            allCompensations: allCompensations as CompensationComponent[],
            isLoading,
            refetch,
            isMasked,
            isAdHocPayrunFlow,
          }}
        >
          <AllPayrunDetails
            payruns={payruns}
            setSelectedPayrun={setSelectedPayrun}
            setPayrunDetailsViewModes={setPayrunDetailsViewModes}
          />
        </PayrunDetailsContext.Provider>
      );
  }
};

export default PayrunDetails;
