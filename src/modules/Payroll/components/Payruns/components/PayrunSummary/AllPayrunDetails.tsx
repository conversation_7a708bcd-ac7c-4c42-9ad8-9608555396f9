import { Delete, InfoOutlined, Paid } from "@mui/icons-material";
import { Box, IconButton, Link, Tooltip, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { MRT_ColumnDef, MRT_RowSelectionState } from "material-react-table";
import React, { Dispatch, SetStateAction, useMemo, useState } from "react";
import { queryClient } from "src/app/App";
import { LogoutIcon } from "src/assets/icons.svg";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import SplitButton from "src/modules/Common/Buttons/SplitButton";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiMask from "src/modules/Common/EffiViews/components/EffiMask";
import ConfirmationModal from "src/modules/Common/Modal/ConfirmationModal";
import DataTable from "src/modules/Common/Table/DataTable";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import DeleteConfirmationModal from "src/modules/Settings/components/Common/DeleteConfirmationModal";
import { PayRunDetail } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { getStatusColors } from "src/utils/typographyUtils";
import MarkAsPaidModal from "../Common/MarkAsPaidModal";
import AddBulkPayrun from "./AddBulkPayrun";
import { PayrunDetailsContext, PayrunDetailsViewModes } from "./PayrunDetails";

interface AllPayrunDetailsProps {
  payruns?: PayRunDetail[];
  setSelectedPayrun: Dispatch<SetStateAction<PayRunDetail | null>>;
  setPayrunDetailsViewModes: (mode: PayrunDetailsViewModes) => void;
}

const DeletePayrunModal = ({
  isDeletePayrunModalOpen,
  closeModal,
  deletePayrunId,
}: {
  isDeletePayrunModalOpen: boolean;
  closeModal: () => void;
  deletePayrunId: string;
}) => {
  const { mutate: deletePayrun } = useMutation({
    mutationFn: (payrunId: string) => payrollService.deletePayRun(payrunId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      closeModal();
    },
  });
  return (
    <ConfirmationModal
      isOpen={isDeletePayrunModalOpen}
      onSubmit={() => deletePayrun(deletePayrunId)}
      onCancel={closeModal}
      title="Delete Pay Run"
    >
      <Typography>Are you sure you want to delete this pay run?</Typography>
    </ConfirmationModal>
  );
};

const AllPayrunDetails: React.FC<AllPayrunDetailsProps> = ({
  payruns,
  setSelectedPayrun,
  setPayrunDetailsViewModes,
}) => {
  const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>({});
  const [deletePayrunId, setDeletePayrunId] = useState<string>("");
  const [payrunAsPaid, setPayrunAsPaid] = useState<string[]>([]);
  const dispatch = useAppDispatch();
  const [isAddBulkPayrunModalOpen, setIsAddBulkPayrunModalOpen] = useState(false);
  const [submitPayrunId, setSubmitPayrunId] = useState<string[] | null>(null);
  const selectedRows = payruns?.filter((_eachPayrun, index) => rowSelection[index]);
  const areActionsEnabled = Object.keys(rowSelection).length > 0;
  const { isMasked, isAdHocPayrunFlow } = React.useContext(PayrunDetailsContext);
  const selectedPayruns = payruns?.filter((_payrun, index) => !!selectedRows?.[index]);

  const markAsPaidMutation = useMutation({
    mutationFn: async (payload: { payrunIds: string[]; payment_mode: string; payment_date: string }) =>
      payrollService.markPayRunAsPaid(payload.payrunIds, payload.payment_mode, payload.payment_date),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      setPayrunAsPaid([]);
      setSelectedPayrun(null);
    },
  });

  const onPayrunDetailClick = (row: any) => {
    setPayrunDetailsViewModes(PayrunDetailsViewModes.EDIT_PAYRUN_DETAIL);
    setSelectedPayrun(row);
    dispatch(setFullviewMode(true));
  };

  const submitPayrunMutation = useMutation({
    mutationFn: (payrunIds: string[]) => payrollService.submitPayrun(payrunIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      setSubmitPayrunId(null);
    },
  });

  const onPayrunSubmit = (ids: string[]) => {
    setSubmitPayrunId(ids);
  };

  const onPayrunAsPaidClick = (payrunIds: string[]) => {
    setPayrunAsPaid(payrunIds);
  };

  const columns = useMemo((): MRT_ColumnDef<PayRunDetail>[] => {
    if (isAdHocPayrunFlow) {
      return [
        {
          accessorKey: "id",
          header: "Pay Run ID",
          minSize: 400,
          Cell: ({ row }) => (
            <Link
              underline="hover"
              onClick={() => onPayrunDetailClick(row?.original)}
              sx={{ cursor: "pointer", color: (theme) => theme.palette.text.link }}
            >
              {row?.original?.id}
            </Link>
          ),
        },
        {
          accessorKey: "active_employees",
          header: "Number of Employees",
        },
        {
          accessorKey: "total_net_pay",
          header: "Net Pay",
          Cell: ({ row }) => <EffiMask isMasked={isMasked}>{formatCurrency(row?.original?.total_net_pay)}</EffiMask>,
        },
        {
          accessorKey: "status",
          header: "Status",
          Cell: ({ row }) => (
            <Box display="flex" gap={0.5} alignItems={"center"}>
              <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>
              {row?.original?.comments && (
                <Tooltip title={row?.original?.comments}>
                  <InfoOutlined fontSize="small" />
                </Tooltip>
              )}
            </Box>
          ),
        },
      ];
    }
    return [
      {
        accessorKey: "id",
        header: "Pay Run ID",
        minSize: 400,
        Cell: ({ row }) => (
          <Link
            underline="hover"
            onClick={() => onPayrunDetailClick(row?.original)}
            sx={{ cursor: "pointer", color: (theme) => theme.palette.text.link }}
          >
            {row?.original?.id}
          </Link>
        ),
      },
      {
        accessorKey: "pay_schedule_name",
        header: "Schedule Name",
        minSize: 300,
      },
      {
        accessorKey: "period",
        header: "Payroll Period",
      },
      {
        accessorKey: "active_employees",
        header: "Number of Employees",
      },
      {
        accessorKey: "total_net_pay",
        header: "Net Pay",
        Cell: ({ row }) => <EffiMask isMasked={isMasked}>{formatCurrency(row?.original?.total_net_pay)}</EffiMask>,
      },
      {
        accessorKey: "status",
        header: "Status",
        Cell: ({ row }) => (
          <Box display="flex" gap={0.5} alignItems={"center"}>
            <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>
            {row?.original?.comments && (
              <Tooltip title={row?.original?.comments}>
                <InfoOutlined fontSize="small" />
              </Tooltip>
            )}
          </Box>
        ),
      },
    ];
  }, [isMasked, isAdHocPayrunFlow]);

  return (
    <Box id="payrun-details" display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        title="Payrun Details"
        actions={
          <SplitButton
            disabled={!areActionsEnabled}
            options={[
              {
                title: "Submit Payrun",
                onClick: () =>
                  onPayrunSubmit(
                    selectedPayruns
                      ?.filter((payrun) => payrun.status === "Draft" || payrun.status === "Sent Back")
                      .map((payrun) => payrun.id) || [],
                  ),
                id: "submit-payrun",
              },
              {
                id: "mark-as-paid",
                title: "Mark as Paid",
                onClick: () => onPayrunAsPaidClick(selectedPayruns?.map((payrun) => payrun.id) || []),
              },
            ]}
          />
        }
      />
      <DataTable
        data={payruns || []}
        enableBatchRowSelection
        enableRowSelection
        onRowSelectionChange={setRowSelection}
        enableRowActions
        positionActionsColumn="last"
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 200,
            maxSize: 200,
            header: "",
            muiTableBodyCellProps: {
              align: "right",
            },
            enablePinning: true,
          },
        }}
        renderRowActions={({ row }) => (
          <Box display="flex" flexDirection="row" gap={1}>
            {!["Submitted", "Paid", "Approved"].includes(row?.original?.status) && (
              <>
                {row?.original?.active_employees > 0 && (
                  <Tooltip title="Submit">
                    <IconButton color="primary" onClick={() => onPayrunSubmit([row?.original?.id])}>
                      <LogoutIcon />
                    </IconButton>
                  </Tooltip>
                )}
                <Tooltip title="Delete Pay Run">
                  <IconButton color="error" size="small" onClick={() => setDeletePayrunId(row?.original?.id)}>
                    <Delete />
                  </IconButton>
                </Tooltip>
              </>
            )}
            {row.original.status === "Approved" && (
              <Tooltip title="Mark as Paid">
                <IconButton color="primary" size="small" onClick={() => onPayrunAsPaidClick([row?.original?.id])}>
                  <Paid />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        )}
        state={{
          rowSelection: rowSelection,
          columnPinning: {
            right: ["mrt-row-actions"],
          },
        }}
        layoutMode="grid"
        columns={columns}
      />
      {isAddBulkPayrunModalOpen && selectedRows && (
        <AddBulkPayrun
          isModalOpen={isAddBulkPayrunModalOpen}
          onClose={() => setIsAddBulkPayrunModalOpen(false)}
          selectedRows={selectedRows}
        />
      )}
      <DeletePayrunModal
        isDeletePayrunModalOpen={!!deletePayrunId}
        closeModal={() => setDeletePayrunId("")}
        deletePayrunId={deletePayrunId}
      />
      {payrunAsPaid.length > 0 && (
        <MarkAsPaidModal
          isOpen={payrunAsPaid.length > 0}
          onClose={() => {
            setPayrunAsPaid([]);
            setSelectedPayrun(null);
            setRowSelection({});
          }}
          onMarkAsPaid={({ value }) => {
            markAsPaidMutation.mutate({
              payrunIds: payrunAsPaid,
              payment_mode: value.paymentType,
              payment_date: value.paymentDate,
            });
          }}
          selectedPayruns={payrunAsPaid}
        />
      )}
      {submitPayrunId && (
        <DeleteConfirmationModal
          onCancel={() => setSubmitPayrunId(null)}
          onDelete={() => submitPayrunMutation.mutate(submitPayrunId)}
          isModalOpen={!!submitPayrunId}
          selectedRole={submitPayrunId}
          title="Are you sure you want to submit this pay run?"
          suffix="submitted"
          saveButtonTitle="Submit"
        />
      )}
    </Box>
  );
};

export default AllPayrunDetails;
