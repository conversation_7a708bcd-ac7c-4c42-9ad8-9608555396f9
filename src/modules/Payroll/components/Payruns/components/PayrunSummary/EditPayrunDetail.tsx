import { useMutation, useQuery } from "@tanstack/react-query";
import React from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { PayRunDetail, UpdateEmployeeDetails } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import EmployeeDetailWithCompensationComponents from "../Common/EmployeeDetailWithCompensationComponents";
import ViewPayrunDetails from "../Common/ViewPayrunDetails";
import { PayrunDetailsContext, PayrunDetailsViewModes } from "./PayrunDetails";

export type EditPayrunDetailProps = {
  payrun: PayRunDetail | null;
  setPayrunDetailsViewModes: (mode: PayrunDetailsViewModes) => void;
};

export enum EditPayrunDetailModes {
  VIEW,
  EDIT,
}

const EditPayrunDetail: React.FC<EditPayrunDetailProps> = ({ payrun, setPayrunDetailsViewModes }) => {
  const [editPayrunDetailMode, setEditPayrunDetailMode] = React.useState<EditPayrunDetailModes | null>(
    EditPayrunDetailModes.VIEW,
  );
  const { refetch: refetchPayrunDetails } = React.useContext(PayrunDetailsContext);
  const [employeeToEdit, setEmployeeToEdit] = React.useState<PayRunDetail | null>(null);

  const onCancel = () => {
    setEditPayrunDetailMode(EditPayrunDetailModes.VIEW);
  };

  const updateEmployeeMutation = useMutation({
    mutationKey: ["update-employee-payrun-detail"],
    mutationFn: async (payload: UpdateEmployeeDetails) => payrollService.updateEmployeeDetails(payload),
  });

  const {
    data: payRunDetails,
    isLoading,
    isFetched,
    refetch,
  } = useQuery(["get-payroll-by-id", payrun?.id], async () => payrollService.getPayrunById(payrun?.id));

  const onSubmit = (request: any, callback: () => void) => {
    updateEmployeeMutation.mutate(request, {
      onSuccess: () => {
        callback();
        refetch();
        refetchPayrunDetails();
      },
    });
  };

  switch (editPayrunDetailMode) {
    case EditPayrunDetailModes.VIEW:
      return (
        <ViewPayrunDetails
          payrun={payrun}
          payRunDetails={payRunDetails}
          isLoading={isLoading}
          isFetched={isFetched}
          refetch={refetch}
          setPayrunDetailsViewModes={setPayrunDetailsViewModes}
          setEmployeeToEdit={setEmployeeToEdit}
          setEditPayrunDetailMode={setEditPayrunDetailMode}
        />
      );
    case EditPayrunDetailModes.EDIT:
      return (
        <>
          <ContentHeader
            title={`Edit Employee Pay Run (${payrun?.period})`}
            showBackButton
            goBack={() => setEditPayrunDetailMode(EditPayrunDetailModes.VIEW)}
          />
          {employeeToEdit && (
            <EmployeeDetailWithCompensationComponents
              employeeToEdit={employeeToEdit}
              setEditPayrunDetailMode={setEditPayrunDetailMode}
              onSubmit={onSubmit}
              onCancel={onCancel}
              onCompute={() => {}}
              components={[]}
            />
          )}
        </>
      );
    default:
      return null;
  }
};

export default EditPayrunDetail;
