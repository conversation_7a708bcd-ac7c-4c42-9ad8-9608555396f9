import { Box } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import payrollService from "src/services/payroll.service";

const PayrunHistory = () => {
  const [selectedRows, setSelectedRows] = useState({});
  const { data, isLoading, isFetched } = useQuery(["get-payrun-history"], async () =>
    payrollService.getPayrunHistory(),
  );
  if (!data) {
    return null;
  }
  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <ContentHeader primaryAction={() => {}} buttonTitle="Export" />
      <DataTable
        data={data || []}
        enableBatchRowSelection
        enableRowSelection
        onRowSelectionChange={setSelectedRows}
        enableRowActions
        positionActionsColumn="last"
        renderRowActions={() => (
          <TableActions
            view={{
              onClick: () => {},
            }}
            edit={{
              onClick: () => {},
              hide: true,
            }}
            remove={{
              onClick: () => {},
              hide: true,
            }}
          />
        )}
        columns={[
          {
            accessorKey: "id",
            header: "Pay Run Id",
          },
          {
            accessorKey: "pay_schedule_name",
            header: "Pay Schedule",
          },
          {
            accessorKey: "pay_run_type",
            header: "Pay Run Type",
          },
          {
            accessorKey: "period",
            header: "Period",
          },
          {
            accessorKey: "status",
            header: "Status",
          },
          {
            accessorKey: "created_by",
            header: "Created By",
          },
        ]}
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 150,
          },
        }}
        state={{
          showSkeletons: isLoading || !isFetched,
          rowSelection: selectedRows,
        }}
      />
    </Box>
  );
};

export default PayrunHistory;
