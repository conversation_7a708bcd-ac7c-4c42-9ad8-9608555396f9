import { Box } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import NoData from "src/modules/Dashboard/component/QuickViews/components/NoDataScreens/NoData";
import payrollService from "src/services/payroll.service";
import PayrunDetails from "./PayrunSummary/PayrunDetails";

const AdHocPayRuns = () => {
  const [isExcludedEmployeesView, setIsExcludedEmployeesView] = React.useState(false);
  const { data, refetch } = useQuery({
    queryKey: ["get-adhoc-payruns"],
    queryFn: async () => payrollService.getAdhocPayruns(),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  if (!data) {
    return <NoData title="No payruns found" />;
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <PayrunDetails
        payruns={data}
        isAdHocPayrunFlow
        refetch={refetch}
        isExcludedEmployeesView={isExcludedEmployeesView}
        setIsExcludedEmployeesView={setIsExcludedEmployeesView}
        isMasked={false}
      />
    </Box>
  );
};

export default AdHocPayRuns;
