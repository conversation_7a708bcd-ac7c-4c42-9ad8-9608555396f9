import { CopyAll, DownloadForOfflineOutlined } from "@mui/icons-material";
import { Alert, Box, Chip, IconButton, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { differenceInDays, format } from "date-fns";
import React, { useMemo } from "react";
import { FROM_LAST_MONTH } from "src/app/constants";
import SplitButton, { ButtonOption } from "src/modules/Common/Buttons/SplitButton";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import { Aggregates, PayRunDetail } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { getStatusColors } from "src/utils/typographyUtils";
import { enrichBaseSummaries } from "../PayrunSummary";
import AdHocPay from "../PayrunSummary/AdHocPay";
import { EditPayrunDetailModes, EditPayrunDetailProps } from "../PayrunSummary/EditPayrunDetail";
import ExcludedPayrunEmployees from "../PayrunSummary/ExcludedPayrunEmployees";
import IncludedPayrunEmployees from "../PayrunSummary/IncludedPayrunEmployees";
import { PayrunDetailsViewModes } from "../PayrunSummary/PayrunDetails";
import SummaryDetails, { TSummaryDetail } from "../PayrunSummary/SummaryDetails";

export const summaryConfig: TSummaryDetail<Partial<Aggregates>>[] = [
  {
    title: "Payrolls Cost",
    value: "",
    trend: "10%",
    isPositive: true,
    trendLabel: FROM_LAST_MONTH,
    type: "base",
    key: "total_gross_pay",
    format: "currency",
  },
  {
    title: "Employees Net Pay",
    value: "",
    trend: "10%",
    isPositive: false,
    trendLabel: FROM_LAST_MONTH,
    type: "base",
    key: "total_net_pay",
    format: "currency",
  },
  {
    title: "Active Employees",
    value: "",
    type: "base",
    trendLabel: FROM_LAST_MONTH,
    key: "active_employees",
    format: "number",
  },
  {
    title: "Pay Day",
    value: "",
    type: "base",
    trendLabel: "In a few days",
    key: "pay_date",
    format: "number",
    subBaseActions: (value: string | number) =>
      differenceInDays(new Date(value), new Date()) < 0 ? (
        ""
      ) : (
        <Typography color="primary">In {differenceInDays(new Date(value), new Date())} days</Typography>
      ),
    formatter: (value: string | number) => (value ? format(value, "MMM dd, yyyy") : ""),
  },
];

const baseConfig: TSummaryDetail<Partial<Aggregates>>[] = [
  {
    title: "Earnings",
    value: 0,
    type: "base",
    format: "currency",
    key: "total_earnings",
  },
  {
    title: "Statutory deductions",
    value: 0,
    type: "base",
    key: "total_statutory_deductions",
    format: "currency",
  },
  {
    title: "Total TDS",
    value: 0,
    type: "base",
    key: "total_taxes",
    format: "currency",
  },
  {
    title: "Base Days",
    value: 0,
    type: "base",
    key: "base_days",
    format: "currency",
  },
];

const ViewPayrunDetails: React.FC<
  EditPayrunDetailProps & {
    setEmployeeToEdit: React.Dispatch<React.SetStateAction<PayRunDetail | null>>;
    setEditPayrunDetailMode: React.Dispatch<React.SetStateAction<EditPayrunDetailModes | null>>;
    payRunDetails: PayRunDetail | null;
    isLoading: boolean;
    isFetched: boolean;
    refetch: () => void;
  }
> = ({
  payrun,
  payRunDetails,
  isLoading,
  isFetched,
  refetch,
  setPayrunDetailsViewModes,
  setEmployeeToEdit,
  setEditPayrunDetailMode,
}) => {
  const [selectedRows, setSelectedRows] = React.useState<Record<number, boolean>>({});
  const [activeTab, setActiveTab] = React.useState(0);
  const [isAdHocPayModalOpen, setIsAdHocPayModalOpen] = React.useState(false);

  const includedEmployeesInPayrun = useMemo(
    () => payRunDetails?.details?.filter((eachDetail) => eachDetail.included_in_pay_run) || [],
    [payRunDetails],
  );
  const excludedEmployeesInPayrun = useMemo(
    () => payRunDetails?.details?.filter((eachDetail) => !eachDetail.included_in_pay_run) || [],
    [payRunDetails],
  );
  const includedEmployeesInSelectedRows = useMemo(() => {
    return Object.keys(selectedRows)
      .map(Number)
      .map((key) => includedEmployeesInPayrun?.[key as any]);
  }, [selectedRows, includedEmployeesInPayrun]);

  const excludedEmployeesInSelectedRows = useMemo(() => {
    return Object.keys(selectedRows)
      .map(Number)
      .map((key) => excludedEmployeesInPayrun?.[key as any]);
  }, [selectedRows, excludedEmployeesInPayrun]);

  const onExcludeEmployee = (ids: string[], isBatch: boolean = false) => {
    excludeEmployeeMutation.mutate(ids);
  };

  const onIncludeEmployee = (ids: string[], isBatch: boolean = false) => {
    includeEmployeeMutation.mutate(ids);
  };

  const onRefreshEmployee = (ids: string[], isBatch: boolean = false) => {
    refreshEmployeePayrunDataMutation.mutate(ids);
  };

  const onAdhocPay = () => {
    console.log({ includedEmployeesInSelectedRows });
    setIsAdHocPayModalOpen(true);
  };

  const optionsMap: Record<number, ButtonOption[]> = useMemo(() => {
    return {
      0: [
        {
          id: "ad-hoc-pay",
          title: "Ad hoc Pay",
          onClick: onAdhocPay,
          disabled: !Object.values(selectedRows).some(Boolean) || !includedEmployeesInSelectedRows.length,
        },
        {
          id: "exclude-employees",
          title: "Exclude Employees",
          onClick: () => onExcludeEmployee(includedEmployeesInSelectedRows.map((eachEmployee) => eachEmployee.id)),
          disabled: !Object.values(selectedRows).some(Boolean) || !includedEmployeesInSelectedRows.length,
        },
        {
          id: "refresh-pay-run-data",
          title: "Refresh Pay Run Data",
          onClick: () => onRefreshEmployee(includedEmployeesInSelectedRows.map((eachEmployee) => eachEmployee.id)),
        },
      ],
      1: [
        {
          id: "include-employees",
          title: "Include Employees",
          onClick: () => onIncludeEmployee(excludedEmployeesInSelectedRows.map((eachEmployee) => eachEmployee.id)),
          disabled: !Object.values(selectedRows).some(Boolean) || !excludedEmployeesInSelectedRows.length,
        },
      ],
    };
  }, [includedEmployeesInSelectedRows, excludedEmployeesInSelectedRows]);

  const excludeEmployeeMutation = useMutation({
    mutationKey: ["exclude-employees-from-payrun"],
    mutationFn: async (employeePayrunIds: string[]) => payrollService.excludeEmployeesFromPayrun(employeePayrunIds),
    onSuccess: () => {
      refetch();
      setSelectedRows({});
    },
  });

  const includeEmployeeMutation = useMutation({
    mutationKey: ["include-employees-in-payrun"],
    mutationFn: async (employeePayrunIds: string[]) => payrollService.includeEmployeesInPayrun(employeePayrunIds),
    onSuccess: () => {
      refetch();
      setSelectedRows({});
    },
  });

  const refreshEmployeePayrunDataMutation = useMutation({
    mutationKey: ["refresh-employee-payrun-data"],
    mutationFn: async (employeePayrunIds: string[]) => payrollService.refreshEmployeePayrunData(employeePayrunIds),
    onSuccess: () => {
      refetch();
      setSelectedRows({});
    },
  });

  if (!payrun) {
    return null;
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        title={
          <Box display="flex" gap={0.5} alignItems="center">
            <Typography variant="body1" fontWeight={600}>{`Pay Run (${payrun.period})`}</Typography>
            <Chip color="primary" variant="filled" label={payrun.pay_run_type} size="small" />
            <Chip
              sx={{ color: getStatusColors(payrun.status) }}
              variant="outlined"
              label={payrun.status}
              size="small"
            />
          </Box>
        }
        showBackButton
        goBack={() => setPayrunDetailsViewModes(PayrunDetailsViewModes.VIEW_ALL_PAYRUN_DETAILS)}
      />
      <Box display="flex" gap={1} alignItems="center">
        <Typography>Pay Run Id: </Typography>
        <Chip
          icon={
            <IconButton
              onClick={() => {
                navigator.clipboard.writeText(payrun.id);
              }}
            >
              <CopyAll />
            </IconButton>
          }
          variant="filled"
          color="default"
          label={payrun.id}
          sx={{ fontWeight: 600 }}
        />
      </Box>
      <Box>
        <SummaryDetails
          baseSummaries={enrichBaseSummaries(payRunDetails as PayRunDetail, summaryConfig)}
          subBaseSummaries={enrichBaseSummaries(payRunDetails as PayRunDetail, baseConfig)}
        />
      </Box>
      <ContentHeader
        title="Employee Details"
        actions={
          <Box display="flex" alignItems="center" gap={1}>
            <IconButton color="primary" size="large">
              <DownloadForOfflineOutlined color="primary" fontSize="large" />
            </IconButton>
            <SplitButton
              disabled={
                !Object.values(selectedRows).some(Boolean) || ["Submitted", "Paid", "Approved"].includes(payrun.status)
              }
              key={activeTab}
              options={optionsMap[activeTab]}
            />
          </Box>
        }
      />
      {excludedEmployeesInPayrun?.length > 0 && (
        <Alert severity="warning">
          You have {excludedEmployeesInPayrun.length} employees excluded from the pay run. Employees excluded from the
          pay run will not be included in the pay run. You can include them back by selecting them and clicking on the
          Include Employees button.
        </Alert>
      )}
      <TabsView
        tabs={[
          {
            id: 0,
            label: "Included Employees",
            component: (
              <>
                <IncludedPayrunEmployees
                  payRunDetails={includedEmployeesInPayrun || []}
                  isLoading={isLoading}
                  isFetched={isFetched}
                  setEmployeeToEdit={setEmployeeToEdit}
                  setEditPayrunDetailMode={setEditPayrunDetailMode}
                  onExcludeEmployee={onExcludeEmployee}
                  onRefreshEmployee={onRefreshEmployee}
                  selectedRows={selectedRows}
                  setSelectedRows={setSelectedRows}
                  hideActions={["Submitted", "Paid", "Approved"].includes(payrun.status)}
                />
                {isAdHocPayModalOpen && (
                  <AdHocPay
                    isModalOpen={isAdHocPayModalOpen}
                    onClose={() => setIsAdHocPayModalOpen(false)}
                    selectedRows={includedEmployeesInSelectedRows}
                  />
                )}
              </>
            ),
          },
          {
            id: 1,
            label: "Excluded Employees",
            component: (
              <ExcludedPayrunEmployees
                payRunDetails={excludedEmployeesInPayrun || []}
                isLoading={isLoading}
                isFetched={isFetched}
                onIncludeEmployee={onIncludeEmployee}
                selectedRows={selectedRows}
                setSelectedRows={setSelectedRows}
                hideActions={["Submitted", "Paid", "Approved"].includes(payrun.status)}
              />
            ),
          },
        ]}
        handleTabChange={(currentTabIndex: number) => {
          setActiveTab(currentTabIndex);
          setSelectedRows({});
        }}
      />
    </Box>
  );
};

export default ViewPayrunDetails;
