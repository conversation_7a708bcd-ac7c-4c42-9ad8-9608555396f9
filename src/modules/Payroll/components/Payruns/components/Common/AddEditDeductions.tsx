import { Box, Button, Paper } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import React, { useEffect } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { CompensationComponent, PayrunEmployeeCompensationDetail } from "src/services/api_definitions/payroll.service";
import { z } from "zod";
import { PayrunDetailsContext } from "../PayrunSummary/PayrunDetails";

interface AddEditDeductionProps {
  isModalOpen: boolean;
  onClose: () => void;
  selectedRow: PayrunEmployeeCompensationDetail;
  allCompensations?: CompensationComponent[];
  onSubmit: (values: any) => void;
}

const bulkSupplementaryPaySchema = z.object({
  component_type: z.enum(["Deduction"]),
  pay_type: z.string().optional().nullable(),
  adjustment_note: z.string().optional().nullable(),
  source: z.enum(["AD_HOC"]),
  formula: z.object({
    calculation_type: z.enum(["Flat", "Percentage"]),
    value: z.number(),
    display_name: z.string().optional().nullable(),
  }),
});

const AddEditDeductions: React.FC<AddEditDeductionProps> = ({ isModalOpen, onClose, selectedRow, onSubmit }) => {
  if (!isModalOpen) return null;
  const isEdit = !!selectedRow;
  const { selectedOrganisationDetails } = useAppSelector((state) => state.userManagement);
  const { allCompensations, isLoading } = React.useContext(PayrunDetailsContext);
  const isAdHocComponent = (component: CompensationComponent) => {
    return !!(component?.attributes?.find((eachAttribute) => Reflect.has(eachAttribute, "ad_hoc")) as any)?.["ad_hoc"];
  };
  const adHocDeductions = allCompensations
    ?.filter(
      (eachCompensation) =>
        eachCompensation?.component_type === "Deduction" &&
        eachCompensation?.active &&
        isAdHocComponent(eachCompensation),
    )
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));
  const deductions = allCompensations
    ?.filter(
      (eachCompensation) =>
        eachCompensation?.component_type === "Deduction" &&
        eachCompensation?.active &&
        !isAdHocComponent(eachCompensation),
    )
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));

  useEffect(() => {
    if (selectedOrganisationDetails?.compensation_basis === "gross") {
      deductions.push({ label: "Gross", value: "Gross" });
    }
    if (selectedOrganisationDetails?.compensation_basis === "ctc") {
      deductions.push({ label: "CTC", value: "CTC" });
    }
  }, [selectedRow, selectedOrganisationDetails]);

  const form = useAppForm({
    defaultValues: {
      ...selectedRow,
      component_type: selectedRow?.component_type || "Deduction",
      pay_type:
        allCompensations?.find((eachCompensation) => eachCompensation?.id === selectedRow?.compensation_component_id)
          ?.name || "",
      formula: selectedRow?.formula || {
        calculation_type: "Flat",
        value: 0,
        display_name: null,
      },
      adjustment_note: selectedRow?.adjustment_note || "",
      source: selectedRow?.source || "AD_HOC",
    },
    validators: {
      onSubmit: bulkSupplementaryPaySchema as any,
    },
    onSubmit: ({ value }) => {
      onSubmit(value);
    },
  });
  const { formula } = useStore(form.store, (state) => state.values);

  return (
    <Modal
      isOpen={isModalOpen}
      title={`${isEdit ? "Edit" : "Add"} Deduction`}
      onClose={onClose}
      showBackButton
      showDivider
      fullwidth
      actions={
        <Box display="flex" p={2} gap={1} justifyContent="flex-end">
          <Button variant="outlined" onClick={() => onClose()}>
            Cancel
          </Button>
          <Button variant="contained" type="submit" onClick={form.handleSubmit}>
            Save
          </Button>
        </Box>
      }
    >
      <Box display="flex" flexDirection="column" gap={1}>
        <form.AppField
          name="pay_type"
          listeners={{
            onChange: ({ value }: { value: string }) => {
              form.setFieldValue(
                "formula",
                allCompensations.find((eachCompensation) => eachCompensation?.name === value)?.formula,
              );
              form.setFieldValue(
                "compensation_component_id",
                allCompensations.find((eachCompensation) => eachCompensation?.name === value)?.id,
              );
            },
          }}
        >
          {(field: any) => (
            <field.EffiSelect label="Deduction Type" disabled={isLoading || isEdit} options={adHocDeductions} />
          )}
        </form.AppField>
        <Box component={Paper} elevation={2} p={2} display="flex" flexDirection="column" gap={2}>
          <form.AppField name="formula.calculation_type">
            {(field: any) => (
              <field.EffiRadioGroup
                label="Calculation Type"
                layout="horizontal"
                disabled
                options={[
                  { label: "Flat Amount", value: "Flat" },
                  { label: "Percentage", value: "Percentage" },
                ]}
              />
            )}
          </form.AppField>
          <Box display="flex" gap={1}>
            <form.AppField name="formula.value">
              {(field: any) => {
                if (formula?.calculation_type === "Percentage") {
                  return (
                    <field.EffiPercentageField
                      label=""
                      required
                      placeholder="Enter percentage"
                      endHelperText={`of ${formula?.display_name}`}
                    />
                  );
                }
                return <field.EffiCurrency label="" required currency="INR" placeholder="Enter amount" />;
              }}
            </form.AppField>
          </Box>
        </Box>
        <form.AppField name="adjustment_note">
          {(field: any) => <field.EffiTextField label="Adjustment Note" />}
        </form.AppField>
      </Box>
    </Modal>
  );
};

export default AddEditDeductions;
