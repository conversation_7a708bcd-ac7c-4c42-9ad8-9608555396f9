import { Add, ExpandMore } from "@mui/icons-material";
import { Accordion, AccordionDetails, AccordionSummary, IconButton, Paper, Typography } from "@mui/material";
import { Box } from "@mui/system";
import React from "react";
import { useContentHeight } from "src/customHooks/useContentHeight";
import { useMasterData } from "src/customHooks/useMasterData";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { CompensationComponent, PayrunEmployeeCompensationDetail } from "src/services/api_definitions/payroll.service";
import { PayrunDetailsContext } from "../PayrunSummary/PayrunDetails";

type Props = {
  components: CompensationComponent[];
  addComponentType: (component: PayrunEmployeeCompensationDetail) => void;
};

const translations = {
  componentLibrary: "Component Library",
  buildYourPayTemplates: "Build your pay templates using active components below.",
  noComponents: "No components to display",
};

const CompensationComponentTypes: React.FC<Props> = ({ addComponentType, components }) => {
  const { allCompensations: allComponents } = React.useContext(PayrunDetailsContext);
  const height = useContentHeight();
  const { data: componentTypes = [], isLoading: isComponentTypeLoading } = useMasterData("CompensationComponentType");

  const onAddComponentClick = (component: CompensationComponent) => {
    addComponentType({
      id: component.id,
      name: component.name,
      amount: component.formula.value,
      adjustment_note: null,
      component_type: component.component_type,
      source: "AD_HOC",
    });
  };

  const isAdHocComponent = (component: CompensationComponent) => {
    return !!(component?.attributes?.find((eachAttribute) => Reflect.has(eachAttribute, "ad_hoc")) as any)?.["ad_hoc"];
  };

  const getComponentsToDisplay = (componentType: string) => {
    return allComponents?.filter(
      (eachComponent) =>
        eachComponent?.component_type === componentType &&
        eachComponent?.active &&
        isAdHocComponent(eachComponent) &&
        !components?.find((_eachComponent) => _eachComponent?.name === eachComponent?.name),
    );
  };

  return (
    <Paper elevation={2} sx={{ height: height - 120, maxHeight: height, overflow: "auto", padding: 2 }}>
      <ContentHeader title={translations.componentLibrary} subtitle={translations.buildYourPayTemplates} />
      {componentTypes &&
        !isComponentTypeLoading &&
        componentTypes.map((eachComponentType: any) => (
          <Accordion key={eachComponentType} sx={{ marginTop: "10px" }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography>{eachComponentType}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {!getComponentsToDisplay(eachComponentType)?.length && (
                <Typography variant="body2">{translations.noComponents}</Typography>
              )}
              {getComponentsToDisplay(eachComponentType)?.map((eachComponent: any) => (
                <Box component={Paper} key={eachComponent.id} elevation={2} gap={1} margin="8px 0px" padding="4px 8px">
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Typography variant="body2">{eachComponent.name}</Typography>
                    <IconButton color="primary" onClick={() => onAddComponentClick(eachComponent)}>
                      <Add />
                    </IconButton>
                  </Box>
                </Box>
              ))}
            </AccordionDetails>
          </Accordion>
        ))}
    </Paper>
  );
};
export default CompensationComponentTypes;
