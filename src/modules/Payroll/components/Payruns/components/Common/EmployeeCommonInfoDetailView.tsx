import { Grid2, Paper } from "@mui/material";
import React from "react";
import DetailListItem from "src/modules/PerformanceManagement/components/DetailListItem";

const employeeDetails = [
  {
    key: "employee.display_name",
    label: "Employee Name",
  },
  {
    key: "employee.cost_center",
    label: "Cost Center",
  },
  {
    key: "employee.work_role.band.name",
    label: "Band",
  },
  {
    key: "employee.work_role.level.name",
    label: "Level",
  },
  {
    key: "employee.work_role.grade.name",
    label: "Grade",
  },
  {
    key: "paid_days",
    label: "Payable Days",
  },
  {
    key: "employee.business_unit",
    label: "Business Unit",
  },
  {
    key: "employee.department",
    label: "Department",
  },
  {
    key: "employee.job_title",
    label: "Job Title",
  },
  {
    key: "lop_days",
    label: "LOP Days",
  },
  {
    key: "employee.employment_status",
    label: "Status",
  },
];

const EmployeeCommonInfoDetailView: React.FC = ({ employeePayrunDetails }) => {
  const enrichedEmployeeDetails = employeeDetails
    .map(({ key, ...otherDetails }) => ({
      ...otherDetails,
      key,
      value: key.split(".").reduce((acc, key) => acc?.[key], employeePayrunDetails),
    }))
    .filter((eachDetail) => {
      const whitelistedLabels = ["lop_days", "employee.employment_status"];
      if (whitelistedLabels.includes(eachDetail.key)) {
        return true;
      }
      return !!eachDetail.value;
    });
  return (
    <Grid2 container spacing={2} component={Paper} elevation={2} padding={2} sx={{ backgroundColor: "#F8F8F8" }}>
      {enrichedEmployeeDetails.map((eachDetail) => (
        <Grid2 key={eachDetail.label} size={2}>
          <DetailListItem
            key={eachDetail.value as unknown as Key}
            title={eachDetail.label}
            value={eachDetail.value as unknown as string}
          />
        </Grid2>
      ))}
    </Grid2>
  );
};

export default EmployeeCommonInfoDetailView;
