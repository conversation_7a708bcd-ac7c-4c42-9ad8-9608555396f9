import { Box, Divider } from "@mui/material";
import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import AdHocPayRuns from "./AdHocPayRuns";
import PayrunHistory from "./PayrunHistory";
import PayrunSummary from "./PayrunSummary";

const tabs = [
  {
    label: "Regular Pay Runs",
    component: <PayrunSummary />,
    id: 0,
  },
  {
    label: "Ad Hoc Pay Runs",
    component: <AdHocPayRuns />,
    id: 1,
  },
  {
    label: "History",
    component: <PayrunHistory />,
    id: 2,
  },
];

const Payruns = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  return (
    <Box display="flex" flexDirection="column" gap={1}>
      {!isFullView && <ContentHeader title="Pay Runs" />}
      {!isFullView && <Divider orientation="horizontal" />}
      <TabsView tabs={tabs} hideTabBar={isFullView} />
    </Box>
  );
};

export default Payruns;
