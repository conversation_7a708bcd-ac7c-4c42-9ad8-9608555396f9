import { Box, Button, Typography, Chip, Autocomplete } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format, isPast, startOfMonth } from "date-fns";
import React, { useEffect, useState } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import useDebounce from "src/customHooks/useDebounce";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { getEnumValues } from "src/modules/Employees/utils/utils";
import { PayRunData } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import SearchServiceAPI from "src/services/search.service";
import tenantsService from "src/services/tenants.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { getFinancialYearStartDate } from "src/utils/dateUtils";
import { z } from "zod";

const AddEditComponent = ({
  selectedRow,
  setSelectedRow,
  // payScheduleData,
}: {
  selectedRow: PayRunData;
  setSelectedRow: (row: PayRunData | null) => void;
  // payScheduleData: PayRunData[];
}) => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setFullviewMode(true));
    return () => {
      dispatch(setFullviewMode(false));
    };
  }, []);

  const { data: costCenters } = useQuery(["cost-center-details"], async () => tenantsService.getCostCenterDetails(), {
    // enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const employeeTypeOptions = getEnumValues("EmployeeType");
  const { selectedOrganisationDetails } = useAppSelector((state) => state.userManagement);
  const addressOptions = selectedOrganisationDetails?.addresses.map((item) => ({
    label: item.display_address,
    value: item.display_address,
  }));

  const costCenterOptions = costCenters?.map((item) => ({
    label: item.code,
    value: item.code,
  }));

  const attendanceLockDayOptions = Array.from({ length: 22 }, (_, i) => ({
    label: i + 1,
    value: i + 1,
  }));

  const isEdit = !!selectedRow?.name;

  const formValidator = z
    .object({
      name: z.string().nonempty({
        message: "Pay Schedule Name is required",
      }),
      frequency: z.string().nonempty({
        message: "Pay Frequency is required",
      }),
      pay_day_rule: z.string().nonempty({
        message: "Pay Day Configuration is required",
      }),
      first_pay_date: z.string().nonempty({
        message: "First Pay Date is required",
      }),
      grace_period_days: z.number().min(1, {
        message: "Grace Period must be greater than 0",
      }),
      attendance_lock_day: z.number().min(1, {
        message: "Attendance Lock Day must be greater than 0",
      }),
      employee_types: z.array(z.string()).nonempty({
        message: "At least one employee type is required",
      }),
      cost_centers: z.array(z.string()).nonempty({
        message: "At least one cost center is required",
      }),
      organisation_address: z.string().nonempty({
        message: "Organisation Address is required",
      }),
      pay_day: z.number().optional().nullable(),
      approval_required_levels: z.number().min(0).max(3),
      approval_matrix: z.array(z.object({
        approval_level: z.number(),
        approver_emails: z.array(z.string().email()),
      })).optional(),
      // custom_pay_day: z.number().min(1, {
      //   message: "Custom Pay Day must be greater than 0",
      // }),
    })
    .refine(
      (data) => {
        const isCustomPayDay = data?.pay_day_rule === "Custom";
        return isCustomPayDay ? !!data?.pay_day : true;
      },
      {
        message: "Custom Pay Day is required",
        path: [""],
      },
    );

  const createPaySchedule = useMutation({
    mutationKey: ["create-pay-schedule"],
    mutationFn: async (payload: PayRunData) => {
      const resp = await payrollService.createPaySchedule(payload);
      return resp;
    },
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const updatePaySchedule = useMutation({
    mutationKey: ["update-pay-schedule"],
    mutationFn: async (payload: PayRunData) => {
      const resp = await payrollService.updatePaySchedule(payload);
      return resp;
    },
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const form = useAppForm({
    defaultValues: {
      name: selectedRow?.name || "",
      frequency: selectedRow?.frequency || "",
      pay_day: selectedRow?.pay_day || 0,
      grace_period_days: selectedRow?.grace_period_days || 0,
      attendance_lock_day: selectedRow?.attendance_lock_day || 0,
      employee_types: selectedRow?.employee_types || [],
      cost_centers: selectedRow?.cost_centers || [],
      organisation_address: selectedRow?.organisation_address || "",
      pay_day_rule: selectedRow?.pay_day_rule || "",
      first_pay_date: selectedRow?.first_pay_date || "",
      approval_required_levels: (selectedRow as any)?.approval_required_levels || 0,
      approval_matrix: (selectedRow as any)?.approval_matrix || [],
    },
    onSubmit: (values) => {
      const firstPayDate = startOfMonth(values?.value?.first_pay_date);
      const payloadValues = {
        ...values?.value,
        pay_day: values?.value?.pay_day_rule === "Custom" ? values?.value?.pay_day : null,
        first_pay_date: format(firstPayDate, "yyyy-MM-dd"),
      };
      if (selectedRow?.name) {
        payloadValues.new_name = values?.value?.name;
        payloadValues.name = selectedRow?.name;
        updatePaySchedule.mutate(payloadValues as any);
      } else {
        createPaySchedule.mutate(payloadValues as any);
      }
    },
    validators: {
      onChange: formValidator as any,
      onSubmit: formValidator as any,
    },
  });

  const { pay_day_rule, attendance_lock_day, grace_period_days, approval_required_levels, approval_matrix } = useStore(form.store, (state: any) => state.values);
  //26 is the max grace period (due to 28 days in february)
  const gracePeriodOptions = Array.from({ length: 26 - attendance_lock_day }, (_, i) => ({
    label: i + 1,
    value: i + 1,
  }));

  const inputFields = [
    {
      fieldProps: {
        name: "organisation_address",
      },
      formProps: {
        label: "Branch",
        type: "select",
        required: true,
        options: addressOptions,
        disabled: isEdit,
      },
      containerProps: {
        size: 6,
      },
    },
    {
      fieldProps: {
        name: "name",
      },
      formProps: {
        label: "Pay Schedule Name",
        type: "text",
        required: true,
      },
      containerProps: {
        size: 6,
      },
    },
    {
      fieldProps: {
        name: "cost_centers",
      },
      formProps: {
        label: "Cost Center",
        type: "multi-select",
        required: true,
        options: costCenterOptions,
        // disabled: isEdit,
      },
      containerProps: {
        size: 6,
      },
    },
    {
      fieldProps: {
        name: "employee_types",
      },
      formProps: {
        label: "Applicable Employee Types",
        type: "multi-select",
        required: true,
        options: employeeTypeOptions.data?.map((item) => ({
          label: item,
          value: item,
        })),
      },
      containerProps: {
        size: 6,
      },
    },
    {
      fieldProps: {
        name: "attendance_lock_day",
        listeners: {
          onChange: (value: any) => {
            if (value?.value + grace_period_days <= 26) return;
            form.setFieldValue("grace_period_days", 26 - value?.value);
            form.validate("change");
          },
        },
      },
      formProps: {
        label: "Attendance Lock Day",
        type: "select",
        required: true,
        options: attendanceLockDayOptions,
      },
      containerProps: {
        size: 3,
      },
    },
    {
      fieldProps: {
        name: "grace_period_days",
      },
      formProps: {
        label: "Grace Period(In Days)",
        type: "select",
        required: true,
        options: gracePeriodOptions,
      },
      containerProps: {
        size: 3,
      },
    },
    {
      fieldProps: {
        name: "frequency",
      },
      formProps: {
        label: "Pay Frequency",
        type: "select",
        required: true,
        options: [
          {
            label: "Monthly",
            value: "Monthly",
          },
        ],
        disabled: isEdit && selectedRow?.has_pay_run,
      },
      containerProps: {
        size: 3,
      },
    },
    {
      fieldProps: {
        name: "first_pay_date",
      },
      formProps: {
        label: "First Pay Month",
        type: "date",
        required: true,
        disabled: isEdit && selectedRow?.has_pay_run,
        format: "MMM yyyy",
        views: ["month", "year"],
        minDate: getFinancialYearStartDate(),
        slotProps: {
          textField: {
            id: "first_pay_date",
            size: "small",
            fullWidth: true,
            placeholder: "First Pay Month",
          },
        },
      },
      containerProps: {
        size: 3,
      },
    },
    {
      fieldProps: {
        name: "pay_day_rule",
      },
      formProps: {
        label: "Pay Day Configuration",
        type: "radio-group",
        options: [
          {
            label: "Last working day of the month",
            value: "Last working day of the month",
          },
          {
            label: "First working day of the next month",
            value: "First working day of the next month",
          },
          {
            label: "Custom",
            value: "Custom",
            subLabel:
              "If the scheduled payday falls on a non-working day or holiday, salaries will be processed on the preceding working day.",
          },
        ],
        required: true,
      },
      containerProps: {
        size: 12,
      },
    },
    {
      fieldProps: {
        name: "approval_required_levels",
      },
      formProps: {
        label: "Approval Levels",
        type: "select",
        required: true,
        options: [
          { label: "0 - No Approval Required", value: 0 },
          { label: "1 - Single Level Approval", value: 1 },
          { label: "2 - Two Level Approval", value: 2 },
          { label: "3 - Three Level Approval", value: 3 },
        ],
      },
      containerProps: {
        size: 12,
      },
    },
  ];

  const customPayDayOptions = [
    {
      fieldProps: {
        name: "pay_day",
      },
      formProps: {
        label: "",
        type: "select",
        required: true,
        options: Array.from({ length: 28 }, (_, i) => ({
          label: i + 1,
          value: i + 1,
        })),
        // size: "small",
      },
      containerProps: {
        size: 12,
      },
    },
  ];

  const goBack = () => {
    setSelectedRow(null);
  };

  // Custom Multi-Select Employee Autocomplete Component
  const EmployeeMultiSelect = ({
    label,
    value = [],
    onChange,
    required = false
  }: {
    label: string;
    value: string[];
    onChange: (emails: string[]) => void;
    required?: boolean;
  }) => {
    const [inputValue, setInputValue] = useState("");
    const [searchOptions, setSearchOptions] = useState<any[]>([]);
    const debouncedValue = useDebounce(inputValue, 400);

    // Search for employees
    const { data: searchResults } = useQuery({
      queryKey: ["employee-search", debouncedValue],
      queryFn: () => {
        if (debouncedValue) {
          return SearchServiceAPI.searchValue(debouncedValue);
        }
        return [];
      },
      enabled: !!debouncedValue,
      refetchOnWindowFocus: false,
    });

    useEffect(() => {
      if (searchResults) {
        setSearchOptions(searchResults);
      }
    }, [searchResults]);

    return (
      <Box display="flex" flexDirection="column" gap={1}>
        <CustomInputLabel title={label} required={required} />
        <Autocomplete
          multiple
          options={searchOptions}
          value={searchOptions.filter(option => value.includes(option.email))}
          onChange={(_, newValue) => {
            const emails = newValue.map((option: any) => option.email);
            onChange(emails);
          }}
          inputValue={inputValue}
          onInputChange={(_, newInputValue) => {
            setInputValue(newInputValue);
          }}
          getOptionLabel={(option) => option.value || ""}
          isOptionEqualToValue={(option, value) => option.email === value.email}
          renderTags={(tagValue, getTagProps) =>
            tagValue.map((option, index) => (
              <Chip
                label={option.value}
                {...getTagProps({ index })}
                key={option.email}
                size="small"
              />
            ))
          }
          renderInput={(params) => (
            <CustomTextField
              {...params}
              placeholder="Search and select employees"
              size="small"
            />
          )}
        />
      </Box>
    );
  };

  // Helper function to render approval level fields
  const renderApprovalLevels = () => {
    if (approval_required_levels === 0) return null;

    const levels = Array.from({ length: approval_required_levels }, (_, i) => i + 1);

    return (
      <Box display="flex" flexDirection="column" gap={2}>
        <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
          Approval Configuration
        </Typography>
        {levels.map((level) => {
          const currentApprovers = approval_matrix?.find((item: any) => item.approval_level === level)?.approver_emails || [];

          return (
            <Box key={level} display="flex" flexDirection="column" gap={1}>
              <EmployeeMultiSelect
                label={`Level ${level} Approvers`}
                value={currentApprovers}
                required={true}
                onChange={(emails: string[]) => {
                  // Update the approval matrix
                  const newMatrix = [...(approval_matrix || [])];
                  const existingIndex = newMatrix.findIndex((item: any) => item.approval_level === level);

                  if (existingIndex >= 0) {
                    newMatrix[existingIndex] = {
                      approval_level: level,
                      approver_emails: emails,
                    };
                  } else {
                    newMatrix.push({
                      approval_level: level,
                      approver_emails: emails,
                    });
                  }

                  form.setFieldValue("approval_matrix", newMatrix);
                }}
              />
            </Box>
          );
        })}
      </Box>
    );
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader title="Pay Schedule" showBackButton goBack={goBack} />
      <EffiDynamicForm form={form} inputFields={inputFields} />
      {pay_day_rule === "Custom" && (
        <Box display="flex" alignItems="center" gap={1} fontSize={14}>
          Day{" "}
          <Box sx={{ width: 80 }}>
            <EffiDynamicForm form={form} inputFields={customPayDayOptions} />
          </Box>{" "}
          of the month
        </Box>
      )}
      {renderApprovalLevels()}
      <form.Subscribe
        selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine, state.isDefaultValue]}
      >
        {([canSubmit, isSubmitting, isPristine, isDefaultValue]) => {
          return (
            <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
              <Button
                variant="contained"
                disabled={
                  !canSubmit || (isSubmitting as boolean) || (isPristine as boolean) || (isDefaultValue as boolean)
                }
                onClick={form.handleSubmit}
              >
                Save
              </Button>
            </Box>
          );
        }}
      </form.Subscribe>
    </Box>
  );
};

export default AddEditComponent;
