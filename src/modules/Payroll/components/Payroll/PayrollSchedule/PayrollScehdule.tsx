import { Box, Button } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import { queryClient } from "src/app/App";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import ConfirmationModal from "src/modules/Common/Modal/ConfirmationModal";
import { PayRunData } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import AddEditComponent from "./AddEditComponent";
import PayScheduleTable from "./PayScheduleTable";

const PayrollSchedule = () => {
  const [selectedRow, setSelectedRow] = useState<PayRunData | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState<string | null>(null);

  const { data: payScheduleData = [] } = useQuery({
    queryKey: ["payScheduleData"],
    queryFn: () => payrollService.getPayScheduleData(),
    enabled: !selectedRow,
    refetchOnWindowFocus: false,
  });

  const { mutate: createPayRun } = useMutation({
    mutationFn: (name: string) => payrollService.createPayRun(name),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["payScheduleData"] });
    },
  });

  const { mutate: activePaySchedule } = useMutation({
    mutationFn: ({ name, isActive }: { name: string; isActive: boolean }) =>
      payrollService.activePaySchedule(name, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["payScheduleData"] });
    },
  });

  const copyPayRun = (row: PayRunData) => {
    setSelectedRow({
      attendance_lock_day: row.attendance_lock_day,
      grace_period_days: row.grace_period_days,
      pay_day_rule: row.pay_day_rule,
      pay_day: row.pay_day,
      first_pay_date: row.first_pay_date,
      frequency: row.frequency,
    } as PayRunData);
  };

  const { mutate: deletePaySchedule } = useMutation({
    mutationFn: (name: string) => payrollService.deletePaySchedule(name),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["payScheduleData"] });
    },
  });

  if (selectedRow) {
    return (
      <AddEditComponent
        selectedRow={selectedRow as PayRunData}
        setSelectedRow={setSelectedRow}
      />
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        title={"Pay Schedule"}
        subtitle={"Configure Pay Schedule"}
        actions={
          <Button onClick={() => setSelectedRow({} as PayRunData)} variant="contained">
            Configure
          </Button>
        }
      />

      <PayScheduleTable
        activePayRun={activePaySchedule}
        payScheduleData={payScheduleData}
        setSelectedRow={setSelectedRow}
        createPayRun={createPayRun}
        copyPayRun={copyPayRun}
        deletePaySchedule={(row: PayRunData) => {
          setDeleteModalOpen(row.name);
        }}
      />
      <ConfirmationModal
        isOpen={!!deleteModalOpen}
        onCancel={() => setDeleteModalOpen(null)}
        onSubmit={() => {
          deletePaySchedule(deleteModalOpen as string);
          setDeleteModalOpen(null);
        }}
        title={"Delete Pay Schedule"}
      >
        <Box>Are you sure you want to delete this pay schedule?</Box>
      </ConfirmationModal>
    </Box>
  );
};

export default PayrollSchedule;
