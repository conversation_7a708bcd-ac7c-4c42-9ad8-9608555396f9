import { Box, Button, Grid2 } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import Modal from "src/modules/Common/Modal/Modal";
import { CompensationComponent } from "src/modules/Employees/Compensation/Compensation";
import { setFullviewMode } from "src/store/slices/app.slice";
import { z } from "zod";
import { useContentHeight } from "../../../../customHooks/useContentHeight";
import { PayrollComponentV2, PayrollTemplateV2 } from "../../../../services/api_definitions/payroll.service";
import payrollService from "../../../../services/payroll.service";
import { useAppForm } from "../../../Common/Form/effiFormContext";
import CompensationComponentTypes from "./CompensationComponentTypes";
import DynamicPayrollInputs from "./DynamicPayrollInputs";
import FixedPayrollInputs from "./FixedPayrollInputs";
import { PayrollTemplateProps, PayrollViewModes } from "./Payroll";
import PreviewTemplate from "./PreviewTemplate";

type Props = {
  isEdit: boolean;
};

export type DefaultFixedFormState = {
  name: string;
  country: string;
  jobTitles: string[];
};

const formSchema = z.object({
  id: z.string().nullish(),
  name: z.string().nonempty({
    message: "Name is required",
  }),
  description: z.string(),
  country: z.string().nonempty({
    message: "Country is required",
  }),
  job_titles: z.array(z.string()).nonempty({
    message: "Job Titles are required",
  }),
  employee_types: z.array(z.string()).nonempty({
    message: "Employee Types are required",
  }),
  aggregates: z.object({
    type: z.enum(["CTC", "GROSS"]),
    value: z.number(),
  }),
  components: z.array(
    z.object({
      id: z.string().nullish(),
      formula: z.object({
        value: z.number().or(z.string()),
      }),
    }),
  ),
});

export const getTemplateDefaultFormState = (selectedRow?: PayrollTemplateV2 | null): DefaultFixedFormState | any => {
  return {
    name: selectedRow?.name || "",
    description: selectedRow?.description || "",
    country: selectedRow?.country || "",
    job_titles: selectedRow?.job_titles || "",
    employee_types: selectedRow?.employee_types || "",
    aggregates: {
      type: "CTC",
      value: 0,
    },
    components:
      selectedRow?.components?.map((eachComponent: PayrollComponentV2) => ({
        ...eachComponent?.compensation_component,
        name: eachComponent?.compensation_component?.name,
        formula: {
          ...eachComponent?.compensation_component?.formula,
        },
      })) || [],
  };
};

const AddEditTemplate: React.FC<PayrollTemplateProps & Props> = ({
  isEdit = false,
  selectedRow,
  setCurrentSelectedMode,
  setSelectedRow,
}) => {
  const height = useContentHeight();
  const dispatch = useAppDispatch();
  const [isPreviewMode, setIsPreviewMode] = React.useState(false);
  console.log("selectedRow", selectedRow);

  const form = useAppForm({
    defaultValues: getTemplateDefaultFormState(selectedRow),
    validators: {
      onSubmit: formSchema,
    },
    onSubmit: (params: any) => {
      const formValues = params.value;
      const requestStructure: Partial<PayrollTemplateV2> = {
        name: formValues.name,
        description: formValues.description,
        country: formValues.country,
        job_titles: formValues.job_titles,
        employee_types: formValues.employee_types,
        components: formValues.components.map((eachComponent: any, idx: number) => {
          return {
            ...eachComponent,
            include_in_ctc: isEdit
              ? eachComponent?.include_in_ctc
              : eachComponent?.component_type === "Benefit"
                ? eachComponent?.include_in_ctc
                : true,
            sort_order: idx,
            formula: {
              ...eachComponent?.formula,
              value: eachComponent?.formula?.value || 0,
            },
          };
        }),
      };
      if (isEdit) {
        updateTemplate.mutate(requestStructure as PayrollTemplateV2);
        return;
      }
      createTemplate.mutate(requestStructure as PayrollTemplateV2);
    },
  });
  const country = useStore(form.store, (state) => state.values.country);
  const components = useStore(form.store, (state) => state.values.components);
  const values = useStore(form.store, (state) => state.values);
  console.log("values", values);

  const { data: allComponents } = useQuery(
    ["get-all-components", country, selectedRow?.name],
    async () => {
      const allCompensationComponents: CompensationComponent[] = await payrollService.getAllCompensationComponents(
        country,
        true,
        true,
      );
      if (!isEdit) {
        allCompensationComponents
          ?.filter((eachComponent) => eachComponent?.mandatory)
          ?.forEach((eachComponent) => {
            form.pushFieldValue("components", { ...eachComponent, include_in_ctc: true });
          });
      }

      return allCompensationComponents;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: true,
      enabled: !!country,
    },
  );

  const createTemplate = useMutation({
    mutationKey: ["create-template"],
    mutationFn: async (payload: PayrollTemplateV2) => payrollService.createTemplate(payload),
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const updateTemplate = useMutation({
    mutationKey: ["update-template"],
    mutationFn: async (payload: PayrollTemplateV2) => payrollService.updateTemplate(payload),
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const goBack = () => {
    setCurrentSelectedMode(PayrollViewModes.VIEW_ALL);
    dispatch(setFullviewMode(false));
    if (setSelectedRow) {
      setSelectedRow(null);
    }
  };

  useEffect(() => {
    dispatch(setFullviewMode(true));
  }, []);

  return (
    <Box>
      <ContentHeader title={isEdit ? "Edit Template" : "Add Template"} showBackButton goBack={goBack} />
      <Grid2 container spacing={2}>
        <Grid2 size={3}>
          <CompensationComponentTypes
            allComponents={allComponents}
            addComponentType={form.pushFieldValue}
            components={components}
          />
        </Grid2>
        <Grid2 size={9}>
          <Box display="flex" flexDirection="column" gap={1}>
            <ContentHeader
              title="Details"
              actions={
                <Box display="flex" alignItems="center" gap={1}>
                  <Button variant="outlined" onClick={() => setIsPreviewMode(!isPreviewMode)}>
                    Preview
                  </Button>
                  <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
                    {([canSubmit, isSubmitting, isPristine]) => (
                      <Box>
                        <Button
                          sx={{ align: "right" }}
                          variant="contained"
                          disabled={!canSubmit || (isSubmitting as boolean) || (isPristine as boolean)}
                          onClick={form.handleSubmit}
                        >
                          Save
                        </Button>
                      </Box>
                    )}
                  </form.Subscribe>
                </Box>
              }
            />
            <Box sx={{ maxHeight: height - 200, overflowY: "auto" }} display="flex" flexDirection="column" gap={2}>
              <FixedPayrollInputs form={form} isEdit={isEdit} />
              <DynamicPayrollInputs form={form} components={components} />
            </Box>
          </Box>
        </Grid2>
      </Grid2>
      <Modal
        isOpen={isPreviewMode}
        onClose={() => setIsPreviewMode(false)}
        actions={
          <Box display="flex" justifyContent="flex-end" gap={2} padding={2}>
            <Button onClick={() => setIsPreviewMode(false)} variant="outlined">
              Cancel
            </Button>
            <Button onClick={form.handleSubmit} variant="contained">
              Save
            </Button>
          </Box>
        }
      >
        <PreviewTemplate componentsToPreview={components} />
      </Modal>
    </Box>
  );
};

export default AddEditTemplate;
