import React from "react";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import ActivePayRuns from "./ActivePayRuns";
import ApprovalHistory from "./ApprovalHistory";

const tabs = [
  {
    id: 0,
    label: "Active Pay Runs",
    component: <ActivePayRuns />,
  },
  {
    id: 1,
    label: "History",
    component: <ApprovalHistory />,
  },
];

const PayrollApprovals = () => {
  return <TabsView tabs={tabs} />;
};

export default PayrollApprovals;
