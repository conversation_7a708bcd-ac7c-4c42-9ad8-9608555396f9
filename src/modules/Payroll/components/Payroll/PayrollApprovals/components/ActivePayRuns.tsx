import { Block, Check, Close } from "@mui/icons-material";
import { Box, IconButton, Tooltip, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useCallback, useMemo } from "react";
import { queryClient } from "src/app/App";
import SplitButton from "src/modules/Common/Buttons/SplitButton";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import ConfirmationModal from "src/modules/Common/Modal/ConfirmationModal";
import DataTable from "src/modules/Common/Table/DataTable";
import payrollService from "src/services/payroll.service";

const ActivePayRuns = () => {
  const [selectedRows, setSelectedRows] = React.useState({});
  const [confirmationModalType, setConfirmationModalType] = React.useState<"approve" | "reject" | "skip" | null>(null);
  const [selectedRowId, setSelectedRowId] = React.useState<string | null>(null);
  const appForm = useAppForm({
    defaultValues: {
      reason: "",
    },
    validators: {
      onSubmit: ({ value }) => {
        if (confirmationModalType === "reject" && !value?.reason) {
          return "Reason is required";
        }
        return null;
      },
    },
  });
  const { reason } = useStore(appForm.store, (state) => state.values);
  const { data, isLoading, isFetching } = useQuery(
    ["get-payrun-approvals"],
    async () => {
      return payrollService.getPayrunApprovals();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
  );

  const selectedRowsInApprovals = useMemo(() => {
    return Object.keys(selectedRows)
      .map(Number)
      .map((index) => data?.[index]);
  }, [selectedRows, data]);

  const approvePayRunMutation = useMutation({
    mutationKey: ["approve-payrun"],
    mutationFn: async (payrunIds: string[]) => payrollService.approvePayrun(payrunIds, reason),
    onSuccess: () => {
      queryClient.invalidateQueries(["get-payrun-approvals"]);
      selectedRowId && setSelectedRowId(null);
    },
  });

  const rejectPayRunMutation = useMutation({
    mutationKey: ["reject-payrun"],
    mutationFn: async (payload: { payrunIds: string[]; reason: string }) =>
      payrollService.rejectPayrun(payload.payrunIds, payload.reason),
    onSuccess: () => {
      queryClient.invalidateQueries(["get-payrun-approvals"]);
      selectedRowId && setSelectedRowId(null);
    },
  });

  const skipPayrunMutation = useMutation({
    mutationFn: (payload: { payrunIds: string[]; reason: string }) =>
      payrollService.skipPayRun(payload.payrunIds, payload.reason),
    onSuccess: () => {
      queryClient.invalidateQueries(["get-payrun-approvals"]);
      selectedRowId && setSelectedRowId(null);
    },
  });

  const onApprovePayrun = useCallback(async (id?: string) => {
    if (id) {
      setSelectedRowId(id);
    }
    setConfirmationModalType("approve");
  }, []);
  const onRejectPayrun = useCallback(async (id?: string) => {
    if (id) {
      setSelectedRowId(id);
    }
    setConfirmationModalType("reject");
  }, []);

  const onSkipPayrun = useCallback(async (id?: string) => {
    if (id) {
      setSelectedRowId(id);
    }
    setConfirmationModalType("skip");
  }, []);

  const onBulkApprovePayrun = useCallback(async () => {
    if (selectedRowId) {
      approvePayRunMutation.mutate([selectedRowId]);
    } else {
      approvePayRunMutation.mutate(selectedRowsInApprovals.map((eachRow) => eachRow?.pay_run_id) as string[]);
    }
    setConfirmationModalType(null);
  }, [selectedRowId, selectedRowsInApprovals, reason]);

  const onBulkRejectPayrun = useCallback(async () => {
    if (selectedRowId) {
      rejectPayRunMutation.mutate({ payrunIds: [selectedRowId], reason });
    } else {
      rejectPayRunMutation.mutate({
        payrunIds: selectedRowsInApprovals.map((eachRow) => eachRow?.pay_run_id) as string[],
        reason,
      });
    }
    setConfirmationModalType(null);
  }, [selectedRowId, selectedRowsInApprovals, reason]);

  const onBulkSkipPayrun = useCallback(async () => {
    if (selectedRowId) {
      skipPayrunMutation.mutate({ payrunIds: [selectedRowId], reason });
    } else {
      skipPayrunMutation.mutate({
        payrunIds: selectedRowsInApprovals.map((eachRow) => eachRow?.pay_run_id) as string[],
        reason,
      });
    }
    setConfirmationModalType(null);
  }, [selectedRowId, selectedRowsInApprovals, reason]);

  const bulkActionMap = {
    approve: {
      title: "Approve Pay Run",
      action: onBulkApprovePayrun,
      confirmation: "Are you sure you want to approve this pay run?",
    },
    reject: {
      title: "Send Back Pay Run",
      action: onBulkRejectPayrun,
      confirmation: "Are you sure you want to send back this pay run? if yes, please provide a reason.",
    },
    skip: {
      title: "Skip Pay Run",
      action: onBulkSkipPayrun,
      confirmation: "Are you sure you want to skip this pay run?",
    },
  };

  return (
    <Box display={"flex"} flexDirection="column" gap={2}>
      <ContentHeader
        title="Pay Run Details"
        actions={
          <SplitButton
            disabled={!Object.values(selectedRows).some(Boolean)}
            options={[
              {
                id: "approve",
                title: "Approve Pay Run",
                disabled: !Object.values(selectedRows).some(Boolean),
                onClick: () => onApprovePayrun(),
              },
              {
                id: "reject",
                title: "Send Back",
                disabled: !Object.values(selectedRows).some(Boolean),
                onClick: () => onRejectPayrun(),
              },
              {
                id: "skip",
                title: "Skip Pay Run",
                disabled: !Object.values(selectedRows).some(Boolean),
                onClick: () => onSkipPayrun(),
              },
            ]}
          />
        }
      />
      <DataTable
        data={data || []}
        state={{
          showSkeletons: isLoading || isFetching,
          rowSelection: selectedRows,
          columnPinning: {
            right: ["mrt-row-actions"],
          },
        }}
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 50,
            maxSize: 50,
            header: "",
            muiTableBodyCellProps: {
              align: "right",
            },
            enablePinning: true,
          },
        }}
        columns={[
          {
            accessorKey: "pay_run_id",
            header: "Pay Run ID",
          },
          {
            accessorKey: "pay_schedule_name",
            header: "Pay Schedule Name",
          },
          {
            accessorKey: "period",
            header: "Period",
          },
          {
            accessorKey: "active_employees",
            header: "Number of Employees",
          },
          {
            accessorKey: "total_gross_pay",
            header: "Gross Pay",
          },
          {
            accessorKey: "submission_date",
            header: "Submission Date",
            Cell: ({ cell }) => cell.getValue<string>(),
          },
        ]}
        enableBatchRowSelection
        enableRowSelection
        enableRowActions
        positionActionsColumn="last"
        onRowSelectionChange={setSelectedRows}
        renderRowActions={({ row }) => (
          <Box display="flex" flexDirection="row" gap={1}>
            <Tooltip title="Approve Pay Run">
              <IconButton color="primary" size="small" onClick={() => onApprovePayrun(row.original.pay_run_id)}>
                <Check />
              </IconButton>
            </Tooltip>
            <Tooltip title="Send Back Pay Run">
              <IconButton color="error" size="small" onClick={() => onRejectPayrun(row.original.pay_run_id)}>
                <Close />
              </IconButton>
            </Tooltip>
            <Tooltip title="Skip Pay Run">
              <IconButton color="primary" size="small" onClick={() => onSkipPayrun(row?.original?.pay_run_id)}>
                <Block />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      />
      {confirmationModalType && (
        <ConfirmationModal
          isOpen={confirmationModalType !== null}
          title={bulkActionMap[confirmationModalType]?.title}
          onSubmit={() => bulkActionMap[confirmationModalType].action()}
          onCancel={() => setConfirmationModalType(null)}
          isSaveDisabled={confirmationModalType === "reject" && !reason}
        >
          <Box display="flex" flexDirection="column" gap={2}>
            <Typography>{bulkActionMap[confirmationModalType]?.confirmation}</Typography>
            <appForm.AppField name="reason">
              {(field: any) => <field.EffiTextField label="Reason" required />}
            </appForm.AppField>
          </Box>
        </ConfirmationModal>
      )}
    </Box>
  );
};

export default ActivePayRuns;
