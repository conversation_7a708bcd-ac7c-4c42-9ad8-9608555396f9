import { Box, Button } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { Option } from "src/app/global";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useMasterData } from "src/customHooks/useMasterData";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import DeleteConfirmationModal from "src/modules/Settings/components/Common/DeleteConfirmationModal";
import { CompensationComponent } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import AddEditBenefits from "./Benefits/AddEditBenefits";
import ViewBenefits from "./Benefits/ViewBenefits";
import AddEditDeductions from "./Deductions/AddEditDeductions";
import ViewDeductions from "./Deductions/ViewDeductions";
import AddEditEarnings from "./Earnings/AddEditEarnings";
import ViewEarnings from "./Earnings/ViewEarnings";

export const getTaxabilityAlertTitles = (taxability?: string | null) => {
  if (taxability === "FULLY_TAXABLE") {
    return "This component is fully taxable";
  }
  if (taxability === "PARTIALLY_EXEMPT") {
    return "This component is partially exempt from tax";
  }
  if (taxability === "FULLY_EXEMPT") {
    return "This component is fully exempt from tax";
  }
  return null;
};

export const CompensationComponentContext = React.createContext({
  refetch: () => {},
  isLoading: false,
  onEdit: (row: CompensationComponent) => {},
  onView: (row: CompensationComponent) => {},
  setSelectedRow: (row: CompensationComponent | null) => {},
  selectedRow: null as CompensationComponent | null,
  softDeleteComponent: (row: CompensationComponent, active: boolean) => {},
  deleteComponent: (row: CompensationComponent) => {},
  appliedFrequencies: [] as Option<string, string>[],
});

const compensationMaps = [
  {
    title: {
      create: "Add Earning",
      edit: "Edit Earning",
    },
    key: "Earning",
    Component: AddEditEarnings,
  },
  {
    title: {
      create: "Add Deduction",
      edit: "Edit Deduction",
    },
    key: "Deduction",
    Component: AddEditDeductions,
  },
  {
    title: {
      create: "Add Benefit",
      edit: "Edit Benefit",
    },
    key: "Benefit",
    Component: AddEditBenefits,
  },
];

const categoriseCompensationComponents = (compensationComponent: CompensationComponent[]) => {
  return compensationComponent?.reduce(
    (acc, item) => {
      if (!acc[item.component_type]) {
        acc[item.component_type] = [];
      }
      acc[item.component_type].push(item);
      return acc;
    },
    {} as Record<string, CompensationComponent[]>,
  );
};

const CompensationComponents = () => {
  const [activeTab, setActiveTab] = React.useState(0);
  const selectedTab = useMemo(() => compensationMaps[activeTab], [activeTab]);
  const [isAddModalOpen, setIsAddModalOpen] = React.useState(false);
  const [rowToDelete, setRowToBeDeleted] = React.useState<CompensationComponent | null>(null);
  const [rowToSoftDelete, setRowToSoftDelete] = React.useState<CompensationComponent | null>(null);
  const [selectedRow, setSelectedRow] = React.useState<CompensationComponent | null>(null);
  const { selectedOrganisationDetails } = useAppSelector((state) => state.userManagement);
  const country = selectedOrganisationDetails?.addresses?.[0]?.country || "India";
  const {
    data: compensationComponents,
    refetch,
    isLoading,
  } = useQuery(
    ["get-compensation-components"],
    async () => {
      const compensationComponents = await payrollService.getAllCompensationComponents(country, false);
      return categoriseCompensationComponents(compensationComponents as CompensationComponent[]) as Record<
        string,
        CompensationComponent[]
      >;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: true,
    },
  );
  const { data: appliedFrequencies } = useMasterData("AppliedFrequency");

  const handleChange = (value: number) => {
    setActiveTab(value);
  };

  const handleAddCompensationTemplate = (_activeTab: number) => {
    setIsAddModalOpen(true);
  };

  const onEdit = (row: CompensationComponent) => {
    setIsAddModalOpen(true);
    setSelectedRow(row);
  };

  const onView = (row: CompensationComponent) => {
    setIsAddModalOpen(true);
  };

  const onSoftDelete = async (id: string, active: boolean) => {
    try {
      await payrollService.changeCompensationComponentStatus(id, active);
    } catch (err) {
      console.log({ err });
    }
    setRowToSoftDelete(null);
    refetch();
  };

  const softDeleteComponent = async (row: CompensationComponent, active: boolean) => {
    if (active) {
      onSoftDelete(row.id, active);
    } else {
      setRowToSoftDelete(row);
    }
  };

  const onDelete = async (id: string) => {
    try {
      await payrollService.deleteCompensationComponent(id);
    } catch (err) {
      console.log({ err });
    }
    setRowToBeDeleted(null);
    refetch();
  };

  const deleteComponent = (row: CompensationComponent) => {
    setRowToBeDeleted(row);
  };

  const getLabelForAppliedFrequency = (value: string) => {
    if (value === "ONE_TIME") {
      return "One time";
    }
    if (value === "RECURRING") {
      return "Recurring for subsequent Payrolls";
    }
    return value;
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        title="Compensation Components"
        actions={
          <Button sx={{ width: 200 }} variant="contained" onClick={() => handleAddCompensationTemplate(activeTab)}>
            {compensationMaps[activeTab]?.title?.create}
          </Button>
        }
      />
      <CompensationComponentContext.Provider
        value={{
          refetch,
          isLoading,
          onEdit,
          onView,
          setSelectedRow,
          selectedRow,
          softDeleteComponent,
          deleteComponent,
          appliedFrequencies:
            appliedFrequencies?.map((each) => ({
              label: getLabelForAppliedFrequency(each as string),
              value: each,
            })) || ([] as any),
        }}
      >
        <TabsView
          handleTabChange={handleChange}
          tabs={[
            {
              id: 0,
              label: "Earnings",
              component: <ViewEarnings earnings={compensationComponents?.Earning || []} />,
            },
            {
              id: 1,
              label: "Deductions",
              component: <ViewDeductions deductions={compensationComponents?.Deduction || []} />,
            },
            {
              id: 1,
              label: "Benefits",
              component: <ViewBenefits benefits={compensationComponents?.Benefit || []} />,
            },
          ]}
        />
        <selectedTab.Component
          isOpen={isAddModalOpen}
          key={selectedRow?.id || ""}
          onClose={() => {
            setIsAddModalOpen(false);
            setSelectedRow(null);
          }}
          title={selectedRow ? selectedTab.title.edit : selectedTab.title.create}
          selectedRow={selectedRow}
          components={compensationComponents?.[selectedTab.key] || []}
        />
      </CompensationComponentContext.Provider>
      <DeleteConfirmationModal
        title="Are you sure you want to delete this component?"
        isModalOpen={!!rowToDelete}
        key={rowToDelete?.id}
        onCancel={() => {
          setRowToBeDeleted(null);
          refetch();
        }}
        onDelete={() => onDelete(rowToDelete?.id as string)}
        selectedRole={rowToDelete?.name || ""}
      />
      <DeleteConfirmationModal
        title="Are you sure you want to deactivate this component?"
        isModalOpen={!!rowToSoftDelete}
        key={rowToSoftDelete?.id}
        onCancel={() => {
          setRowToSoftDelete(null);
          refetch();
        }}
        suffix="deactivated"
        onDelete={() => onSoftDelete(rowToSoftDelete?.id as string, false)}
        selectedRole={rowToSoftDelete?.name || ""}
        saveButtonTitle="Deactivate"
      />
    </Box>
  );
};

export default CompensationComponents;
