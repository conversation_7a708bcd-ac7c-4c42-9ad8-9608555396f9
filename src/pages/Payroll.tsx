import { Box, Tab, Tabs } from "@mui/material";
import React, { useEffect, useMemo } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import useSubroutes from "src/customHooks/useSubroutes";
import NoAccessScreen from "src/modules/Common/NoAccess/NoAccess";
import CompensationComponents from "src/modules/Payroll/components/CompensationComponents/CompensationComponents";
import Payroll from "src/modules/Payroll/components/Payroll/Payroll";
import PayrollApprovals from "src/modules/Payroll/components/Payroll/PayrollApprovals/components/PayrollApprovals";
import PayrollSchedule from "src/modules/Payroll/components/Payroll/PayrollSchedule/PayrollScehdule";
import PayrollDashboard from "src/modules/Payroll/components/PayrollDashboard/PayrollDashboard";
import Payruns from "src/modules/Payroll/components/Payruns/components/Payruns";
import StatutoryComponents from "src/modules/Payroll/components/StatutoryComponents/StatutoryComponents";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { setFullviewMode } from "src/store/slices/app.slice";
import { addBreadcrumb, removeBreadcrumb } from "src/store/slices/breadcrumbs.slice";

const organisationTabs = [
  {
    key: PATH_CONFIG.PAYROLL_DASHBOARD.key,
    path: PATH_CONFIG.PAYROLL_DASHBOARD.path,
    label: "Dashboard",
    component: <PayrollDashboard />,
    id: 0,
  },
  {
    key: PATH_CONFIG.PAYROLL_COMPENSATION_COMPONENTS.key,
    path: PATH_CONFIG.PAYROLL_COMPENSATION_COMPONENTS.path,
    label: "Compensation Components",
    component: <CompensationComponents />,
    id: 1,
  },
  {
    key: PATH_CONFIG.PAYROLL_COMPENSATION_TEMPLATES.key,
    path: PATH_CONFIG.PAYROLL_COMPENSATION_TEMPLATES.path,
    label: "Compensation Templates",
    component: <Payroll />,
    id: 2,
  },
  {
    key: PATH_CONFIG.PAYROLL_PAY_SCHEDULE.key,
    path: PATH_CONFIG.PAYROLL_PAY_SCHEDULE.path,
    label: "Pay Schedule",
    component: <PayrollSchedule />,
    id: 3,
  },
  {
    key: PATH_CONFIG.PAYROLL_PAYRUNS.key,
    path: PATH_CONFIG.PAYROLL_PAYRUNS.path,
    label: "Pay Runs",
    component: <Payruns />,
    id: 4,
  },
  {
    key: PATH_CONFIG.PAYROLL_STATUTORY_COMPONENTS.key,
    path: PATH_CONFIG.PAYROLL_STATUTORY_COMPONENTS.path,
    label: "Statutory",
    component: <StatutoryComponents />,
    id: 5,
  },
  {
    key: PATH_CONFIG.PAYROLL_APPROVALS.key,
    path: PATH_CONFIG.PAYROLL_APPROVALS.path,
    label: "Approvals",
    component: <PayrollApprovals />,
    id: 6,
  },
];

const CompensationPayroll = () => {
  const subRoutes = useSubroutes(PATH_CONFIG.PAYROLL.key);
  const { isFullView } = useAppSelector((state) => state.app);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const tabsToShow = useMemo(
    () => [
      ...organisationTabs
        .filter((payroll) => subRoutes.some((route) => route.key === payroll.key && route.acl?.canRead))
        .sort((a, b) => a.id - b.id),
    ],
    [subRoutes],
  );

  const [value, setValue] = React.useState(tabsToShow?.[0]?.key);

  // This is needed when we are changing roles or orgs & the url remains the same, a fallback check if we may presume
  useEffect(() => {
    const previousSelectedTab = tabsToShow?.find((eachTab) => eachTab.key === searchParams.get("tabId"));

    if (!previousSelectedTab) {
      dispatch(
        addBreadcrumb({
          isActive: true,
          isDisabled: false,
          label: tabsToShow?.[0]?.label,
          path: tabsToShow?.[0]?.key,
        }),
      );
      setValue(tabsToShow?.[0]?.key);
      navigate(PATH_CONFIG.PAYROLL.path);
      return;
    }
    dispatch(
      addBreadcrumb({
        isActive: true,
        isDisabled: false,
        label: previousSelectedTab.label,
        path: previousSelectedTab.key,
      }),
    );
    setValue(previousSelectedTab.key);
    dispatch(setFullviewMode(false));
    navigate(`${PATH_CONFIG.PAYROLL.path}?tabId=${previousSelectedTab.key}`);
  }, []);

  const Component = useMemo(() => tabsToShow.find((eachTab) => eachTab.key === value)?.component, [value]);

  const handleChange = (_event: React.SyntheticEvent, newValue: string) => {
    const tab = tabsToShow.find((eachTab) => eachTab.key === newValue);
    if (tab) {
      dispatch(removeBreadcrumb(tabsToShow.find((eachTab) => eachTab.key === value)?.key || ""));
      dispatch(
        addBreadcrumb({
          isActive: true,
          isDisabled: false,
          label: tab?.label || "",
          path: tab?.key || "",
        }),
      );
      setValue(tab.key);
      navigate(`${PATH_CONFIG.PAYROLL.path}?tabId=${tab.key}`);
    }
  };

  if (!tabsToShow || tabsToShow?.length === 0) {
    return (
      <Box>
        <NoAccessScreen />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: "100%",
        display: "flex",
        justifyContent: "flex-start",
      }}
    >
      {!isFullView && (
        <Tabs
          orientation="vertical"
          variant="scrollable"
          value={value}
          onChange={handleChange}
          aria-label="Vertical tabs example"
          sx={{
            borderRight: 1,
            borderColor: "divider",
            minWidth: "fit-content",
            padding: 0,
          }}
        >
          {tabsToShow.map((tab) => (
            <Tab
              sx={{
                textTransform: "none",
                wordBreak: "break-word",
                width: "100%",
                alignItems: "flex-start",
                fontWeight: 500,
                color: "#667085",
              }}
              id={tab.key}
              value={tab.key}
              key={tab.key}
              label={tab.label}
            />
          ))}
        </Tabs>
      )}
      <Box sx={{ overflow: "auto", width: "100%", padding: isFullView ? 0 : "0px 16px" }}>{Component ?? null}</Box>
    </Box>
  );
};

export default CompensationPayroll;
