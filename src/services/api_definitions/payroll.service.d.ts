import { Employee } from "src/modules/PerformanceManagement/ResourceAllocationTypes";
import Payroll from "../../modules/Settings/components/Payroll/Payroll";
import { CandidateDetails } from "src/modules/Employees/utils/candidateTransformer";

export type PayrollTemplate = {
  name: string;
  country: string;
  job_titles: string[];
  effective_date: string;
  effective_from: string;
  effective_to: string;
  template_name: string;
  components: PayrollComponent[];
};

export type PayrollTemplateV2 = {
  name: string;
  country: string;
  job_titles: string[];
  effective_date: string;
  effective_from: string;
  effective_to: string;
  template_name: string;
  description: string | null;
  active: boolean;
  employee_types: string;
  ctc: number;
  gross: number;
  components: PayrollComponentV2[];
  active: boolean;
};

export type EmployeeAdminConfig = Pick<PayrollTemplate, "components" | "effective_date" | "template_name">;

type PayrollComponent = {
  id: string;
  name: string;
  currency: string;
  amount: number;
  sub_components: PayrollComponent[];
};

export type CompensationAttributeKeys =
  | "consider_for_epf"
  | "consider_for_esi"
  | "include_in_fbp"
  | "ad_hoc"
  | "taxability"
  | "pro_rated";
export type CompensationAttribute = {
  [key: CompensationAttributeKeys]: boolean | string;
  read_only: boolean;
};

type CompensationComponent = {
  id: string;
  compensation_component_id: string;
  name: string;
  assigned: boolean;
  active: boolean;
  code: string;
  currency: string;
  ad_hoc: boolean;
  formula: Formula;
  mandatory: boolean;
  taxable: boolean;
  system_defined: boolean;
  pro_rated: boolean;
  pay_type: string;
  calculation_type: string;
  component_type: string;
  sort_order: null;
  applied_frequency: boolean;
  taxability: "FULLY_TAXABLE" | "PARTIALLY_EXEMPT" | "FULLY_EXEMPT";
  attributes: CompensationAttribute[];
  include_in_ctc: boolean;
};

type PayrollComponentV2 = {
  compensation_component: CompensationComponent;
  amount: number;
};

enum CalculationTypes {
  Flat = "Flat",
  Percentage = "Percentage",
  Formula = "Formula",
  SystemDefined = "System Defined",
}

export type Formulae = {
  value: string;
  code: string | null;
  calculation_type: CalculationTypes;
  display_name: string;
};

export type CreatePayrollComponent = Omit<PayrollTemplate, "components"> & {
  components: PayrollTemplateComponent;
};
export type PayrollTemplateComponent = {
  id: string;
  name: string;
  sub_components: PayrollTemplateComponent[];
};

export type CompensationTemplateDetailRequest = {
  business_unit: string;
  department: string;
  work_role: string;
  job_title: string;
  employee_type: string;
  country: string;
};

export type StatutoryComponent = {
  region: string;
  periodicity: string;
  registration_number: string;
  registration_date: string;
  signatory_name: string;
  signatory_designation: string;
  signatory_email: string | null;
  signatory_phone: {
    country_code: string;
    number: string;
  } | null;
  is_employee: boolean;
  calculation_method_employee: string;
  calculation_method_employer: string;
  attendance_based: boolean;
  can_employee_opt_out: boolean;
  warning_message: string;
};

export type StatutoryComponentRequest = {
  region: string;
  periodicity: string;
  registration_number: string;
  registration_date: string;
  signatory_name: string;
  signatory_designation: string;
  signatory_email: string | null;
  signatory_phone: string | null;
  is_employee: boolean;
  calculation_method_employee: string;
  calculation_method_employer: string;
  attendance_based: boolean;
  can_employee_opt_out: boolean;
};

export type NewStatutoryComponent = {
  region: string;
  periodicity: string;
  registration_number: string;
  registration_date: string;
  signatory_name: string;
  signatory_designation: string;
  signatory_email: string;
};

export type Aggregates = {
  active_employees: number;
  total_gross_pay: number;
  total_net_pay: number;
  total_taxes: number;
  total_statutory_deductions: number;
  excluded_payments: number;
  total_earnings: number;
  total_deductions: number;
  total_benefits: number;
  base_days: number;
  pay_date: Date;
  earnings: number;
  deductions: number;
  benefits: number;
  tax_amount: number;
  gross_pay: number;
  net_pay: number;
  paid_days: number;
};

export interface Payrun
  extends Omit<Aggregates, "total_deductions" | "total_earnings" | "total_benefits" | "base_days"> {
  pay_runs: PayRunDetail[];
}

export interface Benefits {
  current: number;
  percentage_change: null | string;
  is_positive: boolean;
}

export interface PayrunEmployeeDetail {
  employee: CandidateDetails & EmployeeDetails;
  paid_days: number;
  tax_amount: number;
  gross_pay: number;
  earnings: number;
  deductions: number;
  benefits: number;
  net_pay: number;
  id: string;
  taxes: number;
  included_in_pay_run: boolean;
  modified: boolean;
}

export interface PayRunDetail extends Aggregates {
  id: string;
  pay_schedule_name: string;
  status: string;
  period: string;
  pay_date: Date;
  pay_run_type: string;
  created_by: string;
  open: boolean;
  comments: string;
  details: PayrunEmployeeDetail[];
}

export interface PayrunHistory {
  id: string;
  pay_schedule_name: string;
  status: string;
  period: string;
  base_days: number;
  pay_run_type: string;
  total_employees: number;
  total_gross_pay: number;
  total_net_pay: number;
  total_earnings: number;
  total_deductions: number;
  total_benefits: number;
  total_taxes: number;
  created_by: string;
}
export type PayRunData = {
  new_name?: string;
  name: string;
  organisation_address: string;
  frequency: string;
  pay_day_rule: string;
  pay_day: number;
  attendance_lock_day: number;
  grace_period_days: number;
  employee_types: string[];
  cost_centers: string[];
  first_pay_date: string;
  has_active_pay_run: boolean;
  active: boolean;
  has_pay_run: boolean;
};

export type PayrunEmployeeCompensationComponentType = {
  id: string;
  name: string;
  amount: number;
  component_type: string;
  compensation_component_id: string;
  adjustment_note: null;
  source: "REGULAR" | "AD_HOC";
};

export interface PayrunEmployeeCompensationDetail extends PayrunEmployeeDetail {
  components: PayrunEmployeeCompensationComponentType[];
}

type UpdateEmployeeStruct = {
  compensation_component_id: string;
  formula: Formulae;
  adjustment_note: string;
};

export interface UpdateEmployeeDetails {
  employee_pay_run_id: string;
  employee_pay_run_ids?: string[];
  components: UpdateEmployeeStruct[];
}

export interface ExcludedEmployeesPayruns extends Aggregates {
  employee_pay_runs: PayrunEmployeeDetail[];
}

export interface PayrunApprovals {
  pay_run_id: string;
  pay_schedule_name: string;
  period: string;
  active_employees: number;
  total_gross_pay: number;
  submission_date: string;
}
