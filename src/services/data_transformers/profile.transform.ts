import { formatDate, formatDateNormal, formatDateToDayMonthYear } from "src/utils/dateUtils";
import { EmployeeDetails, EntAddress } from "../api_definitions/employees";

class ProfileTransformer {
  extractAddress = (address: EntAddress | undefined): string | null => {
    if (!address) return null;

    return Object.values(address)
      .filter((value) => value !== undefined && value !== null)
      .join(", ");
  };
  profileCardDataAbstractor = (employee: EmployeeDetails | null | undefined) => {
    return {
      name: employee?.display_name || "N/A",
      jobTitle: employee?.job_title || "N/A",
      department: employee?.department || "N/A",
      companyName: employee?.organisation || "N/A",
      email: employee?.email || "N/A",
      dateOfJoining: employee?.date_of_joining || "N/A",
      officeLocation: employee?.office_address?.display_address || "N/A",
      displayPic: employee?.display_pic || "N/A",
      tenure: employee?.tenure || "N/A",
    };
  };
  personalInformationAbstractor = (employee: EmployeeDetails | null | undefined) => {
    return {
      gender: employee?.gender || "-",
      dateOfBirth: employee?.date_of_birth ? formatDateToDayMonthYear(employee?.date_of_birth) : "-",
      mobileNumber: employee
        ? (typeof employee?.phone === "string"
            ? employee?.phone
            : employee?.phone?.country_code + employee?.phone?.number) || "-"
        : "-",
      UAN: employee?.uan || "-",
      PAN: employee?.pan || "-",
      aadhaar: employee?.aadhaar || "-",
      nationality: employee?.nationality || "-",
      bloodGroup: employee?.blood_group || "-",
      maritalStatus: employee?.marital_status || "-",
      passport: employee?.passport || "-",
      currentAddress:
        employee?.current_address?.display_address || this.extractAddress(employee?.current_address) || "-",
      permanentAddress:
        employee?.permanent_address?.display_address || this.extractAddress(employee?.permanent_address) || "-",
      personalEmail: employee?.personal_email || "-",
    };
  };
  educationDetailsAbstractor = (employee: EmployeeDetails | null | undefined) => {
    if (!employee?.education_details) return [];
    return employee?.education_details?.map((education) => ({
      degree: education.degree,
      type: education.degree_type,
      institution: education.institute,
      affiliation: education.university,
      period: `${education.start_year} - ${education.end_year}`,
    }));
  };
  bankDetailAbstractor = (employee: EmployeeDetails | null | undefined) => {
    if (!employee?.bank_account) return {};
    return {
      accountNumber: employee?.bank_account?.account_number || "-",
      accountHolderName: employee?.bank_account?.account_holder_name || "-",
      IFSC_Code: employee?.bank_account?.ifsc || "-",
      bankName: employee?.bank_account?.bank_name || "-",
      branch: employee?.bank_account?.bank_branch || "-",
      // address: employee?.bank_account?.address || "-", // is required as per design, need to see if api call needed or not as value not in Employee response
    };
  };
  familyDetailsAbstractor = (employee: EmployeeDetails | null | undefined) => {
    if (!employee?.dependents) return [];
    return employee?.dependents.map((family) => ({
      firstName: family.first_name,
      relation: family.relation,
      lastName: family.last_name,
      dateOfBirth: formatDateToDayMonthYear(family.date_of_birth as string),
    }));
  };
  emergencyContactsAbstractor = (employee: EmployeeDetails | null | undefined) => {
    if (!employee?.emergency_contacts) return [];
    return employee?.emergency_contacts.map((contact) => ({
      name: contact.name,
      phone: contact
        ? (typeof contact?.phone === "string"
            ? contact?.phone
            : contact?.phone?.country_code + contact?.phone?.number) || "-"
        : "-",
      relation: contact.relation,
      primary: contact?.primary,
    }));
  };
  workExperienceAbstractor = (employee: EmployeeDetails | null | undefined) => {
    if (!employee?.work_experience) return [];
    return employee?.work_experience.map((experience) => {
      const nowDateString = formatDateNormal(new Date().toISOString());
      return {
        designation: experience.designation,
        companyName: experience.company,
        period: `${formatDate(String(experience.from_date))} - ${formatDate(String(experience.to_date || nowDateString))}`,
      };
    });
  };
  profileAdditionalInfo(employee: EmployeeDetails | null | undefined) {
    if (!employee) return [];
    const { band, grade, level } = employee?.work_role || {};
    const userDetails = [
      { title: "Band", value: band?.name },
      { title: "Level", value: level?.name },
      { title: "Grade", value: grade?.name },
      { title: "Status", value: employee.employment_status },
      { title: "Manager", value: employee.manager?.display_name },
      { title: "Business Unit", value: employee.business_unit },
      { title: "Cost Center", value: employee.cost_center },
    ].filter((item) => !!item.value);
    return userDetails;
  }
}

export default new ProfileTransformer();
